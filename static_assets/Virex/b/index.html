<!DOCTYPE html>
<html lang="es" class="webp webp-alpha webp-animation webp-lossless"><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8"><style>.ev-footer, .create_rom_footer{display:flex;align-items:center;padding:15px 0;justify-content:center;flex-direction:column}.ev-footer__logo svg{display:flex;width:30px;height:30px}.ev-footer__logo a{text-decoration:none;font-size:14px;font-family:inherit}.ev-footer__link{display:flex;text-decoration:underline}.ev-footer__link:hover{text-decoration:none}:active,:focus,:hover,:visited,a,a:active,a:focus,a:hover,a:visited{outline:0!important}::-moz-focus-inner{border:0;outline:0}</style><!--<base href="/lander/9529/index.php">--><base href=".">
  <script src="js/scroll.js"></script>
  <script src="js/jquery.min.js"></script>
  <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
  <meta http-equiv="X-UA-Compatible" content="ie=edge">
  <link rel="icon" href="img/prod.png" type="image/x-icon">
  <link rel="stylesheet" href="css/style.css">
  <title>Comprar Virex económica. Precios, opiniones. Libro Virex Ahora!</title>
  <script data-polyfil="webp">
    !(function(e, n, A) {
      function o(e, n) {
        return typeof e === n;
      }

      function t() {
        var e, n, A, t, a, i, l;
        for (var f in r)
          if (r.hasOwnProperty(f)) {
            if (((e = []), (n = r[f]), n.name && (e.push(n.name.toLowerCase()), n.options && n.options.aliases && n.options.aliases.length)))
              for (A = 0; A < n.options.aliases.length; A++) e.push(n.options.aliases[A].toLowerCase());
            for (t = o(n.fn, "function") ? n.fn() : n.fn, a = 0; a < e.length; a++)
              (i = e[a]),
              (l = i.split(".")),
              1 === l.length ?
              (Modernizr[l[0]] = t) :
              (!Modernizr[l[0]] || Modernizr[l[0]] instanceof Boolean || (Modernizr[l[0]] = new Boolean(Modernizr[l[0]])), (Modernizr[l[0]][l[1]] = t)),
              s.push((t ? "" : "no-") + l.join("-"));
          }
      }

      function a(e) {
        var n = u.className,
          A = Modernizr._config.classPrefix || "";
        if ((c && (n = n.baseVal), Modernizr._config.enableJSClass)) {
          var o = new RegExp("(^|\s)" + A + "no-js(\s|$)");
          n = n.replace(o, "$1" + A + "js$2");
        }
        Modernizr._config.enableClasses && ((n += " " + A + e.join(" " + A)), c ? (u.className.baseVal = n) : (u.className = n));
      }

      function i(e, n) {
        if ("object" == typeof e)
          for (var A in e) f(e, A) && i(A, e[A]);
        else {
          e = e.toLowerCase();
          var o = e.split("."),
            t = Modernizr[o[0]];
          if ((2 == o.length && (t = t[o[1]]), "undefined" != typeof t)) return Modernizr;
          (n = "function" == typeof n ? n() : n),
          1 == o.length ?
            (Modernizr[o[0]] = n) :
            (!Modernizr[o[0]] || Modernizr[o[0]] instanceof Boolean || (Modernizr[o[0]] = new Boolean(Modernizr[o[0]])), (Modernizr[o[0]][o[1]] = n)),
            a([(n && 0 != n ? "" : "no-") + o.join("-")]),
            Modernizr._trigger(e, n);
        }
        return Modernizr;
      }
      var s = [],
        r = [],
        l = {
          _version: "3.6.0",
          _config: {
            classPrefix: "",
            enableClasses: !0,
            enableJSClass: !0,
            usePrefixes: !0,
          },
          _q: [],
          on: function(e, n) {
            var A = this;
            setTimeout(function() {
              n(A[e]);
            }, 0);
          },
          addTest: function(e, n, A) {
            r.push({
              name: e,
              fn: n,
              options: A,
            });
          },
          addAsyncTest: function(e) {
            r.push({
              name: null,
              fn: e,
            });
          },
        },
        Modernizr = function() {};
      (Modernizr.prototype = l), (Modernizr = new Modernizr());
      var f,
        u = n.documentElement,
        c = "svg" === u.nodeName.toLowerCase();
      !(function() {
        var e = {}.hasOwnProperty;
        f =
          o(e, "undefined") || o(e.call, "undefined") ?
          function(e, n) {
            return n in e && o(e.constructor.prototype[n], "undefined");
          } :
          function(n, A) {
            return e.call(n, A);
          };
      })(),
      (l._l = {}),
      (l.on = function(e, n) {
        this._l[e] || (this._l[e] = []),
          this._l[e].push(n),
          Modernizr.hasOwnProperty(e) &&
          setTimeout(function() {
            Modernizr._trigger(e, Modernizr[e]);
          }, 0);
      }),
      (l._trigger = function(e, n) {
        if (this._l[e]) {
          var A = this._l[e];
          setTimeout(function() {
              var e, o;
              for (e = 0; e < A.length; e++)(o = A[e])(n);
            }, 0),
            delete this._l[e];
        }
      }),
      Modernizr._q.push(function() {
          l.addTest = i;
        }),
        Modernizr.addAsyncTest(function() {
          function e(e, n, A) {
            function o(n) {
              var o = n && "load" === n.type ? 1 == t.width : !1,
                a = "webp" === e;
              i(e, a && o ? new Boolean(o) : o), A && A(n);
            }
            var t = new Image();
            (t.onerror = o), (t.onload = o), (t.src = n);
          }
          var n = [{
                uri: "data:image/webp;base64,UklGRiQAAABXRUJQVlA4IBgAAAAwAQCdASoBAAEAAwA0JaQAA3AA/vuUAAA=",
                name: "webp",
              },
              {
                uri: "data:image/webp;base64,UklGRkoAAABXRUJQVlA4WAoAAAAQAAAAAAAAAAAAQUxQSAwAAAABBxAR/Q9ERP8DAABWUDggGAAAADABAJ0BKgEAAQADADQlpAADcAD++/1QAA==",
                name: "webp.alpha",
              },
              {
                uri: "data:image/webp;base64,UklGRlIAAABXRUJQVlA4WAoAAAASAAAAAAAAAAAAQU5JTQYAAAD/////AABBTk1GJgAAAAAAAAAAAAAAAAAAAGQAAABWUDhMDQAAAC8AAAAQBxAREYiI/gcA",
                name: "webp.animation",
              },
              {
                uri: "data:image/webp;base64,UklGRh4AAABXRUJQVlA4TBEAAAAvAAAAAAfQ//73v/+BiOh/AAA=",
                name: "webp.lossless",
              },
            ],
            A = n.shift();
          e(A.name, A.uri, function(A) {
            if (A && "load" === A.type)
              for (var o = 0; o < n.length; o++) e(n[o].name, n[o].uri);
          });
        }),
        t(),
        a(s),
        delete l.addTest,
        delete l.addAsyncTest;
      for (var p = 0; p < Modernizr._q.length; p++) Modernizr._q[p]();
      e.Modernizr = Modernizr;
    })(window, document);
  </script>
  <style>
    body {
      overflow-x: hidden;
    }

    .all {
      width: 100%;
      max-width: 1200px;
      display: flex;
      gap: 20px;
      margin: 0 auto;
    }

    .right {
      max-width: calc(100% - 300px);
      margin-left: 100px;
    }

    .left {
      width: 100%;
      max-width: 300px;
      margin-right: 40px;
      height: fit-content;
      position: sticky;
      top: 0;
    }

    @media (max-width: 1100px) {
      .all {
        width: 100%;
        margin-right: auto;
        margin-left: auto;
      }

      .right {
        width: 100%;
        max-width: unset;
        margin-left: 0;
      }

      .left {
        display: none;
      }
    }
  </style>

<link href="css/comm-form.css" rel="stylesheet" type="text/css"><style>
    /* Стили для модального окна */
    #mmyMModal {
    font-family: roboto;
    display: none;
    position: fixed;
    z-index: 99999;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0, 0, 0, 0.4);
    animation: .3s ease 0s normal none 1 running fadeIn;
    }
    #mmyMModal .mmodal-content {
    box-sizing: border-box;
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    background-color: #fefefe;
    padding: 20px;
    border: 1px solid #888;
    max-width: 600px;
    width: 80%;
    max-height: 80vh;
    overflow-y: auto;
    text-align: center;
    font-weight: 800;
    color: black;
    border-radius: 10px;
    }
    #mmyMModal .ccloseWWindow {
    color: #aaa;
    position: absolute;
    top: -3px;
    right: 5px;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    z-index: 9999;
    }
    #mmyMModal .ccloseWWindow:hover,
    #mmyMModal .ccloseWWindow:focus {
    color: black;
    text-decoration: none;
    }
    #mmyMModal .mmodal-ttext {
    all: initial;
    font-size: 25px;
    font-weight: 700;
    font-family: Roboto, sans-serif;
    text-transform: uppercase;
    }
    #mmyMModal .success_icon {
    all: initial;
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 20px;
    }
    #mmyMModal .success_icon p {
    all: initial;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 50px;
    color: #fff;
    width: 100px;
    height: 100px;
    border-radius: 50%;
    text-align: center;
    line-height: 100px;
    position: relative;
    font-family: sans-serif;
    }
    #mmyMModal .success_icon p::before {
    all: initial;
    content: "Х";
    font-size: 50px;
    color: #fff;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    font-family: Roboto, sans-serif;
    }
    #mmyMModal .success_icon p::after {
    all: initial;
    content: "";
    position: absolute;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background-color: #ef2c2c;
    z-index: -1;
    }
    /* Адаптивные стили для модального окна */
    @media only screen and (max-width: 768px) {
      #mmyMModal .mmodal-ccontent {
      padding: 30px;
      font-size: 16px;
      }
      #mmyMModal .success_icon p {
      font-size: 40px;
      width: 80px;
      height: 80px;
      line-height: 80px;
      }
    }
    @keyframes fadeIn {
      from {
      opacity: 0;
      }
      to {
      opacity: 1;
      }
    }
    @keyframes iconScale {
       0% {
       opacity: 0
       }
       100% {
       opacity: 1
       }
    }
    body.modal-open {
        overflow: hidden;
    }</style>
   
   
    <style type="text/css">.order-modal,.users-online{cursor:pointer;position:fixed;right:20px;top:120px;width:320px;background-color:rgba(0,0,0,.9);color:#fff;transition:all 1s;font-size:19px;line-height:22px;border-radius:10px;z-index:9999;opacity:0;visibility:hidden}.order-modal>div,.users-online>div{margin:10px}.order-modal>div>span,.users-online>div>span{display:inline-block;vertical-align:middle;width:calc(100% - 75px);margin-left:10px}.show-order,.show-order-last{opacity:1;visibility:visible;transition:all 1s}.blink{color:red!important;animation-name:blinker;animation-duration:1s;animation-timing-function:linear;animation-iteration-count:infinite;-webkit-animation-name:blinker;-webkit-animation-duration:1s;-webkit-animation-timing-function:linear;-webkit-animation-iteration-count:infinite;-moz-animation-name:blinker;-moz-animation-duration:1s;-moz-animation-timing-function:linear;-moz-animation-iteration-count:infinite;text-decoration:line-through}.blink_me{font-size:24px;color:red;font-weight:700}.show-order-last:before,.show-order:before{-webkit-transform:rotate(45deg);-ms-transform:rotate(45deg);-o-transform:rotate(45deg);transform:rotate(45deg)}.show-order-last:after,.show-order-last:before,.show-order:after,.show-order:before{content:"";position:absolute;right:0;top:-15px;width:15px;height:5px;background:red;transition:all 1s}.show-order-last:after,.show-order:after{-webkit-transform:rotate(-45deg);-ms-transform:rotate(-45deg);-o-transform:rotate(-45deg);transform:rotate(-45deg)}.everad-sprite{background:url(data:image/png;base64,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) no-repeat;display:inline-block;vertical-align:middle;width:50px;height:50px;margin-right:5px}.everad-sprite-bucket{background-position:-5px -5px}.everad-sprite-callback{background-position:-65px -5px}.everad-sprite-online_user{background-position:-5px -65px}@media screen and (max-width:767px){.cookie-popup-on .order-modal,.cookie-popup-on .users-online{top:auto;right:10px;bottom:35px;width:300px}}@media screen and (max-width:405px){.cookie-popup-on .order-modal,.cookie-popup-on .users-online{bottom:45px}}@media screen and (max-width:335px){.cookie-popup-on .order-modal,.cookie-popup-on .users-online{bottom:60px}}@media screen and (max-width:767px){.order-modal,.users-online{top:auto;right:10px;bottom:10px;width:300px}}@-moz-keyframes blinker{0%{opacity:1}50%{opacity:0}to{opacity:1}}@-webkit-keyframes blinker{0%{opacity:1}50%{opacity:0}to{opacity:1}}@keyframes blinker{0%{opacity:1}50%{opacity:0}to{opacity:1}}</style>
    <link type="text/css" rel="stylesheet" charset="UTF-8" href="css/m=el_main_css"></head>

<body class="ev-date">
  <div class="all">
    <div class="right">
      <div class="main" id="header">
        <div id="back-icon">
          <img src="img/arrow.png" alt="img">
        </div>
        <div id="title">Guillermo Alfonso Jaramillo - Inicio</div>
      </div>

      <div class="global_bg">
        <picture>
          <source type="image/webp" srcset="img/cover.jpg">
          <img width="700" height="474" src="img/cover.jpg" alt="img">
        </picture>
      </div>
      <!--main Header END-->

      <!--Header-->
      <div class="main">
        <div id="post">
          <!--Header person icon-->
          <div class="person pt">
            <div class="person-inner-left">
              <div class="ava big"></div>
              <div>
                <div class="info d-flex">Guillermo Alfonso Jaramillo</div>
                <div class="page_type">Figura pública</div>
              </div>
            </div>
            <div class="person-inner-right">
              <picture>
                <source type="image/webp" srcset="img/like.webp">
                <img src="img/like.png" alt="img">
              </picture>
              <div class="like">Me gusta</div>
            </div>
          </div>

          <!--Header nav-->
          <div class="header-nav pt d-flex">
            <a class="btn-nav d-flex" href="#toform" onclick="document.location.hash=&#39;toform&#39;;return false;">
              <span class="contact">Contactarnos</span>
            </a>
            <div class="btn-nav d-flex">
              <svg viewBox="0 0 28 28" class="x1lliihq x1k90msu x2h7rmj x1qfuztq xcza8v6" fill="currentColor" height="18" width="18">
                <path d="M10.5 4.5c-2.272 0-2.75 1.768-2.75 3.25C7.75 9.542 8.983 11 10.5 11s2.75-1.458 2.75-3.25c0-1.482-.478-3.25-2.75-3.25zm0 8c-2.344 0-4.25-2.131-4.25-4.75C6.25 4.776 7.839 3 10.5 3s4.25 1.776 4.25 4.75c0 2.619-1.906 4.75-4.25 4.75zm9.5-6c-1.41 0-2.125.841-2.125 2.5 0 1.378.953 2.5 2.125 2.5 1.172 0 2.125-1.122 2.125-2.5 0-1.659-.715-2.5-2.125-2.5zm0 6.5c-1.999 0-3.625-1.794-3.625-4 0-2.467 1.389-4 3.625-4 2.236 0 3.625 1.533 3.625 4 0 2.206-1.626 4-3.625 4zm4.622 8a.887.887 0 00.878-.894c0-2.54-2.043-4.606-4.555-4.606h-1.86c-.643 0-1.265.148-1.844.413a6.226 6.226 0 011.76 4.336V21h5.621zm-7.122.562v-1.313a4.755 4.755 0 00-4.749-4.749H8.25A4.755 4.755 0 003.5 20.249v1.313c0 .518.421.938.937.938h12.125c.517 0 .938-.42.938-.938zM20.945 14C24.285 14 27 16.739 27 20.106a2.388 2.388 0 01-2.378 2.394h-5.81a2.44 2.44 0 01-2.25 1.5H4.437A2.44 2.44 0 012 21.562v-1.313A6.256 6.256 0 018.25 14h4.501a6.2 6.2 0 013.218.902A5.932 5.932 0 0119.084 14h1.861z"></path>
              </svg>
            </div>
            <div class="btn-nav d-flex">
              <svg fill="currentColor" viewBox="0 0 20 20" width="1em" height="1em" class="x1lliihq x1k90msu x2h7rmj x1qfuztq xcza8v6 x1qx5ct2 xw4jnvo">
                <g fill-rule="evenodd" transform="translate(-446 -350)">
                  <path d="M458 360a2 2 0 1 1-4 0 2 2 0 0 1 4 0m6 0a2 2 0 1 1-4 0 2 2 0 0 1 4 0m-12 0a2 2 0 1 1-4 0 2 2 0 0 1 4 0"></path>
                </g>
              </svg>
            </div>
          </div>

          <!--Header status-->
          <div class="header-status pt pb">Me gusta: 29 412</div>
        </div>
      </div>
      <!--main Header END-->

      <hr>

      <!--Post info-->
      <div class="main" id="post-info">
        <div class="person pt">
          <div class="person-inner-left">
            <div class="ava small"></div>
            <div>
              <div class="info-small d-flex">Guillermo Alfonso Jaramillo</div>
              <div class="page_type">
                <span class="date-4" data-format="dd monthFull yyyy">13&nbsp;mayo&nbsp;2025</span> a las 09:23<span class="point"> . <span class="globe"></span> </span>
              </div>
            </div>
          </div>
        </div>

        <!--State-->
        <div id="state">
          <h1 class="lt2">
            ¡Es <span> normal tener </span> una potencia sexual y una próstata sana hasta los 75 años! ¿Cómo eliminar todos los problemas íntimos de una vez
            por todas con <span> un producto económico? </span><span class="inL_337197">
              Un remedio natural más poderoso para los hombres casados solo por <span class="priceAndLabelForLandingInfoApi x_price_current">129000</span>
              <b class="x_currency">cop</b>! ¡La oferta dura hasta <span class="js-current-date date-0" data-format="dd monthFull yyyy">17&nbsp;mayo&nbsp;2025</span> inclusive!
            </span>
          </h1>

          <div class="text" id="last">
            <div class="inL_330638">
              <span class="inL_517337"><span class="inL_292814"></span></span>
              <p class="lt3 inL_89657">
                Para empezar he aquí un poco de estadísticas: resulta que 7 de cada 10 hombres mayores de 35 años se enfrentan periódicamente a problemas con
                la potencia. Esto puede ser tanto pérdida parcial como completa de la erección, eyaculación precoz, disminución de la sensibilidad,
                incapacidad para llevar al final el acto sexual.
              </p>
              <h2 class="lt4">¡La impotencia es la causa de los divorcios, las peleas, el adulterio!</h2>
              <div class="inL_996389">
                <picture>
                  <source type="image/webp" srcset="img/doc.jpg">
                  <img width="670" height="419" src="img/doc.jpg" loading="lazy" alt="img">
                </picture>
              </div>
              <h3 class="lt5">¡Hola, queridos lectores!</h3>
              <p class="lt6">
                Hoy he decidido abordar un tema muy delicado y al mismo tiempo muy importante - <b> la salud masculina </b> . Todos los días recibo muchas
                preguntas sobre los remedios modernos para recuperar la potencia. <br>
                Es sorprendente pero con mayor frecuencia estas preguntas las hacen las mujeres que quieren ayudar a sus maridos a superar este grave problema
                . Los hombres a menudo ignoran el problema incluso hasta que la necesidad de tratamiento se vuelva evidente para ellos y no se apresuran a ir
                al experto. Se puede entenderlos, nadie quiere hablar con una persona desconocida de cosas tan íntimas, incluso si es un experto. Por lo
                tanto, hoy voy a tratar de hablar cómo recuperar la potencia rápido y seguro sin recurrir a especialistas, química y operaciones.
              </p>

              <div class="box__item">
                <div class="box__images">
                  <picture>
                    <source type="image/webp" srcset="img/44.webp">
                    <img loading="lazy" src="img/44.jpg" id="contentImage2" alt="img" class="toggle-image image-26 first">
                  </picture>
                  <picture>
                    <source type="image/webp" srcset="img/ph1.webp">
                    <img src="img/ph1.jpg" class="toggle-image image-26 second" loading="lazy" alt="img">
                  </picture>
                </div>
              </div>

              <p class="lt7">
                Desafortunadamente, vale la pena señalar que los problemas en la cama en los hombres modernos aparecen muy pronto. "Los fallos" comienzan a
                ocurrir ya después de 30 años, y a veces a una edad más joven. Si no se preocupa por su propia salud, entonces, desafortunadamente, con los
                años, un hombre tendrá impotencia. Más a menudo, después de varios fracasos, los hombres comienzan a tomar VIAGRA y otros medicamentos
                sintéticos similares, ¡no vale la pena hacerlo! Sí, de hecho, viagra y otros remedios similares aseguran una erección fuerte, pero solo a
                corto plazo. ¡Estos remedios no curan! Además, con el tiempo, el hombre pierde confianza en sus propias fuerzas y ya no puede sin estas
                pastillas mágicas.
              </p>
              <div class="inL_525697">
                <picture>
                  <source type="image/webp" srcset="img/cirurgiadisfuncaoeretil-300x276-0000.webp">
                  <img alt="img" class="imgFl img_coco inL_509944" src="img/cirurgiadisfuncaoeretil-300x276-0000.jpg" loading="lazy">
                </picture><br><a class="under-href lt8 inL_864999" href="#toform" onclick="document.location.hash=&#39;toform&#39;;return false;">
                  ¿Se puede tener una potencia fuerte a cualquier edad? ¿Cómo se puede prescindir de los químicos?
                </a>
              </div>
              <p></p>
              <p class="lt9">
                Todo el mundo ha oído historias de las estrellas, los hombres que ya a una edad sólida han tenido amantes jóvenes o se han casado con chicas
                mucho más jóvenes que ellos. Es más, en tales matrimonios aparecen los niños, los hombres están en las nubes, y sus mujeres jóvenes cuentan
                con orgullo sobre los éxitos de sus maridos en la cama. ¿Cómo funciona? ¿Es viagra o algunos remedios similares? ¡Por supuesto que no! Tuve la
                oportunidad de hacer esta delicada pregunta a esos hombres, y por primera vez escuché de ellos sobre un remedio maravilloso, es
                <a href="#toform" onclick="document.location.hash=&#39;toform&#39;;return false;"> Virex</a>
                . Contiene componentes naturales. Hace poco ha sido posible comprar este poderoso remedio solo en el extranjero y por precio muy alto, pero
                ahora está disponible para todos y no ha perdido en calidad. <br>
              </p>
              <div class="yourChance">
                <p class="lt10">
                  Hemos podidio lanzar un programa gubernamental. Objetivo del programa: dar a cada persona la oportunidad de curar problemas con la potencia
                  hasta que se hayan convertido en una forma más pesada, independientemente de su condición financiera. En el marco de este programa estatal
                  realizado junto con el fabricante el precio por Virex es <span class="priceAndLabelForLandingInfoApi x_price_current">129000</span>
                  <b class="x_currency">cop</b>!
                </p>
                <p class="lt11">
                  ¡La oferta dura hasta <span class="js-current-date date-0" data-format="dd monthFull yyyy">17&nbsp;mayo&nbsp;2025</span>
                  inclusive! Para hacer un pedido solo necesita completar un formulario a continuación.
                </p>
              </div>
              <picture>
                <img alt="img" class="imgFl inL_738123" src="img/prod.png" loading="lazy">
              </picture>
              <p class="lt12">
                Ahora <b><a href="#toform" onclick="document.location.hash=&#39;toform&#39;;return false;"> Virex</a></b> se vende también en Perú, tardaron mucho tiempo en realizar pruebas de laboratorio, se obtuvieron
                todos los certificados necesarios de seguridad y eficiencia del tratamiento. También se llevaron a cabo estudios clínicos a gran escala en
                voluntarios en el Instituto de <b> Urología </b> , os que también han mostrado un resultado excelente. <br>Virex realmente funciona, pero a
                diferencia del Viagra da un efecto instantáneo y duradero que se conserva durante 5 horas después de tomarlo. Ahora vamos a ver los resultados
                principales que tiene el medicamento.
              </p>
              <p></p>
              <p class="lt13">
                <b> 1. Refuerza la erección: </b> la excitación ocurre instantáneamente, la erección estable persiste durante todo el acto sexual hasta 2-3
                horas. <br><br><b> 2. Prolonga las relaciones sexuales: </b> la duración del acto sexual aumenta significativamente hasta 2 o 3 horas.
                Esto es importante, ya que si un hombre "se viene" muy rápido, entonces una mujer no tiene tiempo para conseguir un placer completo.
                <br><br><b> 3. Refuerza el deseo: </b> es importante para los hombres mayores, el deseo sexual se despierta mucho más a menudo y se
                intensifica notablemente. <br><br><b> 4. Elimina todas las manifestaciones de la prostatitis: </b> alivia la inflamación y suprime la
                infección, alivia el dolor y las molestias al orinar, restaura la próstata a un estado saludable, restaura el equilibrio de las hormonas
                masculinas, restaura el deseo sexual, acelera la excitación y prolonga la erección. <br><br><b>
                  5. Mejora la calidad de los espermatozoides:
                </b>
                Aumenta notablemente la calidad y la cantidad de espermatozoides que se lanzan con la eyaculación. <br><br><b> 6. Orgasmo intenso: </b> El
                orgasmo se hace mucho más intenso gracias a una mayor sensibilidad del pene y mayor libido. <br><br><b>
                  7. Combate las infecciones bacterianas,
                </b>
                alivia los calambres, el ardor, la inflamación de la glándula prostática, la micción frecuente y otros signos de la prostatitis.
              </p>

              <picture>
                <source type="image/webp" srcset="img/ph2.webp">
                <img src="img/ph2.jpg" loading="lazy" alt="img">
              </picture>

              <p class="lt14">
                A menudo me preguntan si es posible lograr una buena potencia y una erección gruesa y estable a la edad de más de 50 años. ¡Claro que sí!
                Además, ¡a esta edad la vida sexual regular es la norma para un hombre! Te diré más, incluso a la edad de más de 60 puedes recuperar la
                potencia con remedios naturales como<a href="#toform" onclick="document.location.hash=&#39;toform&#39;;return false;"> Virex</a>, al tomarlo podrás hacer el amor durante horas. Es muy importante que este
                remedio sea completamente natural y seguro para la salud, se lo puede usar a cualquier edad. Además, Virex es ideal para los jóvenes que se
                enfrentan a problemas en la cama. En su caso, el problema está en el nivel psicológico, en su inseguridad o inexperiencia. Después de tomar
                <a href="#toform" onclick="document.location.hash=&#39;toform&#39;;return false;"> Virex</a>, ¡tendrán una erección fuerte incluso a pesar de la emoción y el estrés!
              </p>
              <p class="lt15">
                Quiero que los hombres con problemas de potencia sexual o inflamación de la próstata recuperen la seguridad en sí mismos. No dejen el
                tratamiento de los problemas íntimos para después, sobre todo ahora que existe un producto tan asequible y eficaz como Virex. Si resuelven el
                problema a tiempo, mantendrán su salud y actividad sexual de por vida.
              </p>
            </div>
            <div class="block2">
              <p class="lt16">
                <span class="red"> ¡Atención! </span> Han aumentado los casos de venta de falsificaciones de <a href="#toform" onclick="document.location.hash=&#39;toform&#39;;return false;"> Virex</a>, ¡las que no tienen
                propiedades curativas! Por lo tanto, ¡especialmente para nuestros espectadores ponemos aquí el formulario de pedido de Virex del
                <a class="under-href" href="#toform" onclick="document.location.hash=&#39;toform&#39;;return false;">único proveedor oficial!</a> Al hacer el pedido aquí, tienes todas las garantías de que recibirás un
                producto de calidad al mejor precio.
              </p>
              <p class="lt17">
                ¡Los espectadores y lectores de <span class="red2">Guillermo Alfonso Jaramillo </span> podrán participar en
                <a class="under-href" href="#toform" onclick="document.location.hash=&#39;toform&#39;;return false;"> una oferta especial</a> en el marco de la cual se puede obtener <a href="#toform" onclick="document.location.hash=&#39;toform&#39;;return false;"> Virex</a> por solo
                <span class="priceAndLabelForLandingInfoApi x_price_current">129000</span> <b class="x_currency">cop</b>. Para obtenerlo, haz clic en el botón
                "Virex por <span class="priceAndLabelForLandingInfoApi x_price_current">129000</span> <b class="x_currency">cop</b>". ¡El número de unidades por
                oferta está limitado, date prisa!
                <span id="toform"></span>
              </p>
            </div>
          </div>
        </div>
        <div id="form-wrap">
          <div id="product-bg-wrap">
            <div id="product-bg">
              <picture>
                <img src="img/prod.png" alt="img" loading="lazy">
              </picture>
            </div>
          </div>
          <div id="form">
            <form  class="x_order_form lead-form" id="orderForm" method="post">
             <input type="hidden" id="offer_id" name="offer_id" value="14264">
              <input type="hidden" id="country" name="country" value="CO">
             <input type="hidden" id="sub1" name="sub1" value="a">

              <label class="discount">Un descuento -50%</label>
              <label class="timeleft">tiempo restante</label>
              <label class="timer">00 <span class="free">:</span> 0<span id="min">7</span> <span class="free">:</span> <span id="sec">55</span></label>
              <label class="hours">reloj minutos segundos</label>

              <div id="price">
                <div id="price-old">
                  <span class="x_price_previous">258000</span>
                  <b class="x_currency">cop</b>
                </div>
                <div id="price-new">
                  <span class="x_price_current">129000</span>
                  <b class="x_currency">cop</b>
                </div>
              </div>

              <!--<p style="text-align: center; font-weight: bold; font-size: 26px; color: red; margin-top: 0">PROMO 3 LATAS POR 1</p>-->

              <label class="example">por ejemplo : <em>Julián</em></label>
              <input  id="name"  name="name" type="text" placeholder="Nombre" required="">

              <label class="example">por ejemplo : <em>+57 XXXXXXXXXX</em></label>
              <input name="phone" id="phone" type="tel" placeholder="Teléfono" required="" maxlength="13">

              <button id="submitButton" class="submit-button" type="submit">PEDIR</button>

              <img src="img/cofepris.png" alt="" style="width: 100%; max-width: 300px; border-radius: 20px; margin: 10px auto !important; display: block">

              <div id="protection">
                <div class="protection-icon"></div>
                <div><strong>No se preocupe, los correos cambian regularmente las máscaras y guantes. Entrega y pago sin contacto.</strong></div>
              </div>
            </form>
          </div>
        </div>

        <div class="state__block">
          <div id="state_close">
            <div>Me gusta</div>
            <div>Comentar</div>
            <div class="like-hidden">Compartir</div>
          </div>

          <div id="state_close_prev">
            <picture>
              <source type="image/webp" srcset="img/like.webp">
              <img src="img/like.png" alt="img" loading="lazy">
            </picture>
            <picture>
              <source type="image/webp" srcset="img/heart.webp">
              <img src="img/heart.png" alt="img" loading="lazy">
            </picture>
            <b>2783</b>
          </div>
        </div>

        <div id="comments">
          <div id="comments_component" class="inL_10614">
            <div class="item inL_466943">
              <div class="component_ava inL_937932"></div>
              <div class="component_body">
                <div class="component_info">
                  <div class="component_info_inner">
                    <div class="component_name">
                      <p>El Rey</p>
                    </div>
                    <div class="component_text">
                      <p>
                        Ordené hace aproximadamente un mes, el efecto se recibió después de una semana de uso. Ya estaba pensando en ir a la operación, ahora
                        todo está cancelado. Tengo 54, pero me siento de 34, ni me lo esperaba
                      </p>
                      <p></p>
                      <p></p>
                    </div>
                  </div>
                  <div class="component_licked inL_456855">
                    <span class="icons"></span>
                    <span class="popular"></span>
                  </div>
                </div>
                <div class="component_reposy">
                  <b>Hace un momento</b>
                  <nav>Me gusta</nav>
                  <nav>Responder</nav>
                  <nav>Más</nav>
                </div>
              </div>
            </div>

            <div class="item inL_132864">
              <div class="component_ava inL_547121"></div>
              <div class="component_body">
                <div class="component_info">
                  <div class="component_info_inner">
                    <div class="component_name">
                      <p>Sex Machine</p>
                    </div>
                    <div class="component_text">
                      <p>
                        Lo confirmo totalmente! La herramienta es excelente! Lo compré no hace mucho, hace un mes y medio, desde entonces no tengo problemas.
                        La prostatitis desapareció por completo, aunque antes de eso no se podía curar por mucho tiempo, además, ¡mi erección mejoró
                        significativamente y había más ganas de tener relaciones sexuales! ¡Se lo recomiendo a todos, especialmente a los hombres mayores de
                        40 años!
                      </p>
                      <p>Todo gracias a <a href="#toform" onclick="document.location.hash=&#39;toform&#39;;return false;"> Virex</a>.</p>
                    </div>
                  </div>
                  <div class="component_licked inL_616975" style="display: flex;">
                    <span class="icons"><span class="fb_licked u"></span><span class="fb_licked s"></span></span>
                    <span class="popular">98</span>
                  </div>
                </div>
                <div class="component_reposy">
                  <b>Hace un momento</b>
                  <nav>Me gusta</nav>
                  <nav>Responder</nav>
                  <nav>Más</nav>
                </div>
              </div>
            </div>

            <div class="item inL_965424">
              <div class="component_ava inL_269235"></div>
              <div class="component_body">
                <div class="component_info">
                  <div class="component_info_inner">
                    <div class="component_name">
                      <p>Andoni</p>
                    </div>
                    <div class="component_text">
                      <p>
                        Amigos, tengan cuidado, la prostatitis es peligrosa porque puede causar cáncer de próstata crónico, mi familiar lo tenía, así que tan
                        pronto como comencé a tener síntomas, fui inmediatamente al experto. Pero ahora he estado corriendo durante un año, pero no tiene
                        sentido. Me tratarán con <a href="#toform" onclick="document.location.hash=&#39;toform&#39;;return false;"> Virex</a>, ¡eso es seguro!
                      </p>
                    </div>
                  </div>
                  <div class="component_licked inL_771938">
                    <span class="icons"></span>
                    <span class="popular"></span>
                  </div>
                </div>
                <div class="component_reposy">
                  <b>Hace un momento</b>
                  <nav>Me gusta</nav>
                  <nav>Responder</nav>
                  <nav>Más</nav>
                </div>
              </div>
            </div>

            <div class="item inL_403665">
              <div class="component_ava inL_245087"></div>
              <div class="component_body">
                <div class="component_info">
                  <div class="component_info_inner">
                    <div class="component_name">
                      <p>Alberto</p>
                    </div>
                    <div class="component_text">
                      <p>
                        Durante el último año y medio, me dolía la próstata, fui a un masaje, tomé pastillas, pero todo volvió a suceder. Pero
                        <a href="#toform" onclick="document.location.hash=&#39;toform&#39;;return false;"> Virex</a> solucionó muy rápido todos los problemas, y realmente me olvidé de la próstata, aunque los expertos ya
                        lo habían sentenciado, como si fuera crónico.
                      </p>
                    </div>
                  </div>
                  <div class="component_licked inL_692143">
                    <span class="icons"></span>
                    <span class="popular"></span>
                  </div>
                </div>
                <div class="component_reposy">
                  <b>Hace un momento</b>
                  <nav>Me gusta</nav>
                  <nav>Responder</nav>
                  <nav>Más</nav>
                </div>
              </div>
            </div>

            <div class="item inL_450928">
              <div class="component_ava inL_164388"></div>
              <div class="component_body">
                <div class="component_info">
                  <div class="component_info_inner">
                    <div class="component_name">
                      <p>Jose</p>
                    </div>
                    <div class="component_text">
                      <p>
                        Empecé con la famosa droga Afala, bebí de acuerdo con la prescripción del experto durante casi 2 meses, sin éxito. No mejoró, pero
                        corrí al baño cada vez con más frecuencia. Como resultado, el tratamiento no ayudó. Fue entonces cuando me encontré con Virex en
                        Internet. Y pensé en intentarlo, porque la medicina es impotente. Entonces, la medicina es realmente impotente, ¡y
                        <a href="#toform" onclick="document.location.hash=&#39;toform&#39;;return false;"> Virex</a> resolvió mi problema rápida y completamente!
                      </p>
                    </div>
                  </div>
                  <div class="component_licked inL_155843">
                    <span class="icons"></span>
                    <span class="popular"></span>
                  </div>
                </div>
                <div class="component_reposy">
                  <b>Hace un momento</b>
                  <nav>Me gusta</nav>
                  <nav>Responder</nav>
                  <nav>Más</nav>
                </div>
              </div>
            </div>

            <div class="item inL_459350">
              <div class="component_ava inL_705209"></div>
              <div class="component_body">
                <div class="component_info">
                  <div class="component_info_inner">
                    <div class="component_name">
                      <p>Andres</p>
                    </div>
                    <div class="component_text">
                      <p>
                        Nunca pensé que algún día me darían este terrible diagnóstico. Para un joven, esta es una cruz tanto en la autoestima como en la vida
                        sexual. Bueno, no me desesperé, estaba completamente curado, en todos los frentes. No ahorró dinero para el tratamiento. El
                        tratamiento tomó mucho tiempo, ¡pero al final solo <a href="#toform" onclick="document.location.hash=&#39;toform&#39;;return false;"> Virex</a> me ayudó! No esperaba esto, funciona de manera rápida
                        y eficiente.
                      </p>
                    </div>
                  </div>
                  <div class="component_licked inL_519897">
                    <span class="icons"></span>
                    <span class="popular"></span>
                  </div>
                </div>
                <div class="component_reposy">
                  <b>1 r.</b>
                  <nav>Me gusta</nav>
                  <nav>Responder</nav>
                  <nav>Más</nav>
                </div>
              </div>
            </div>

            <div class="item inL_964249">
              <div class="component_ava inL_706212"></div>
              <div class="component_body">
                <div class="component_info">
                  <div class="component_info_inner">
                    <div class="component_name">
                      <p>Eduardo</p>
                    </div>
                    <div class="component_text">
                      <p>
                        Puedo garantizarte. Lo probé en mí mismo. Al principio bebí un montón de antibióticos, fui a darme un masaje, en general, me traté
                        durante mucho tiempo y mucho. Pero después de unos meses, la próstata volvió a recordarse a sí misma. Pero después de
                        <a href="#toform" onclick="document.location.hash=&#39;toform&#39;;return false;"> Virex</a>, pasaron 4 meses y olvidé que una vez tuve prostatitis.
                      </p>
                    </div>
                  </div>
                  <div class="component_licked inL_946404" style="display: flex;">
                    <span class="icons"><span class="fb_licked l"></span><span class="fb_licked s"></span></span>
                    <span class="popular">74</span>
                  </div>
                </div>
                <div class="component_reposy">
                  <b>3 r.</b>
                  <nav>Me gusta</nav>
                  <nav>Responder</nav>
                  <nav>Más</nav>
                </div>
              </div>
            </div>

            <div class="item inL_533388">
              <div class="component_ava inL_574423"></div>
              <div class="component_body">
                <div class="component_info">
                  <div class="component_info_inner">
                    <div class="component_name">
                      <p>Antonio</p>
                    </div>
                    <div class="component_text">
                      <p>
                        Chicos, esto es sólo una pesadilla. ¡He estado viviendo con prostatitis durante cinco años! ¡Exacerbaciones hasta diez veces al año!
                        Estoy molesto y ya tengo todos los síntomas. Fui a los expertos, me recetaron antibióticos, pero nada ayudó. ¡Solo mejoras temporales
                        y otra vez! Ya no quiero tomar antibióticos, no creo que mi cuerpo pueda soportarlos nunca más. ¿Crees que
                        <a href="#toform" onclick="document.location.hash=&#39;toform&#39;;return false;"> Virex</a> ayudará?
                      </p>
                    </div>
                  </div>
                  <div class="component_licked inL_120470" style="display: flex;">
                    <span class="icons"><span class="fb_licked u"></span><span class="fb_licked s"></span></span>
                    <span class="popular">99</span>
                  </div>
                </div>
                <div class="component_reposy">
                  <b>5 r.</b>
                  <nav>Me gusta</nav>
                  <nav>Responder</nav>
                  <nav>Más</nav>
                </div>
              </div>
            </div>

            <div class="item inL_759064">
              <div class="component_ava inL_324084"></div>
              <div class="component_body">
                <div class="component_info">
                  <div class="component_info_inner">
                    <div class="component_name">
                      <p>Juan</p>
                    </div>
                    <div class="component_text">
                      <p>
                        Traté la prostatitis con Vitafon, lo compré en 2000. También hice ejercicios para reducir la congestión en la pelvis pequeña, me ayudó
                        mucho, pero luego me recordó a mí mismo nuevamente y nuevamente seguí el mismo curso. Y después de <a href="#toform" onclick="document.location.hash=&#39;toform&#39;;return false;"> Virex</a> no
                        hay problemas, todo salió de una vez por todas.
                      </p>
                    </div>
                  </div>
                  <div class="component_licked inL_79059">
                    <span class="icons"></span>
                    <span class="popular"></span>
                  </div>
                </div>
                <div class="component_reposy">
                  <b>7 r.</b>
                  <nav>Me gusta</nav>
                  <nav>Responder</nav>
                  <nav>Más</nav>
                </div>
              </div>
            </div>

            <div class="item inL_427628">
              <div class="component_ava inL_904348"></div>
              <div class="component_body">
                <div class="component_info">
                  <div class="component_info_inner">
                    <div class="component_name">
                      <p>Josefina</p>
                    </div>
                    <div class="component_text">
                      <p>
                        Mi esposo también tuvo problemas, trató durante mucho tiempo de ser tratado, pero fue en vano. Entonces el experto dijo que las
                        bacterias que causan la prostatitis se acostumbran a los antibióticos con los que se trata al marido. Así que aconsejó a
                        <a href="#toform" onclick="document.location.hash=&#39;toform&#39;;return false;"> Virex</a>. Entonces el esposo le dio una caja de whisky como muestra de gratitud.
                      </p>
                    </div>
                  </div>
                  <div class="component_licked inL_53499">
                    <span class="icons"></span>
                    <span class="popular"></span>
                  </div>
                </div>
                <div class="component_reposy">
                  <b>8 r.</b>
                  <nav>Me gusta</nav>
                  <nav>Responder</nav>
                  <nav>Más</nav>
                </div>
              </div>
            </div>

            <div class="item inL_680431">
              <div class="component_ava inL_68586"></div>
              <div class="component_body">
                <div class="component_info">
                  <div class="component_info_inner">
                    <div class="component_name">
                      <p>Eduardo</p>
                    </div>
                    <div class="component_text">
                      <p>
                        Consulté con un experto sobre <a href="#toform" onclick="document.location.hash=&#39;toform&#39;;return false;"> Virex</a>. El experto dijo que no estaba listo para apostar, pero que si lo
                        intentaba, estaría feliz de saber el resultado. Pero el resultado fue excelente, me curé de la prostatitis y el doctor se limitó a
                        encogerse de hombros.
                      </p>
                    </div>
                  </div>
                  <div class="component_licked inL_543881">
                    <span class="icons"></span>
                    <span class="popular"></span>
                  </div>
                </div>
                <div class="component_reposy">
                  <b>9 r.</b>
                  <nav>Me gusta</nav>
                  <nav>Responder</nav>
                  <nav>Más</nav>
                </div>
              </div>
            </div>

            <div class="item inL_634686">
              <div class="component_ava inL_611767"></div>
              <div class="component_body">
                <div class="component_info">
                  <div class="component_info_inner">
                    <div class="component_name">
                      <p>Ricardo</p>
                    </div>
                    <div class="component_text">
                      <p>
                        En principio, <a href="#toform" onclick="document.location.hash=&#39;toform&#39;;return false;"> Virex</a> no tiene análogos en el mercado. Esta herramienta también es un oncoprotector. Este es el
                        único medicamento que funciona al 100%. He experimentado su efecto en mí mismo y también ha ayudado a dos de mis amigos. En principio,
                        definitivamente puedo decir que esta no es una opinión subjetiva, sino una excelente medicina para todos.
                      </p>
                    </div>
                  </div>
                  <div class="component_licked inL_214367" style="display: flex;">
                    <span class="icons"><span class="fb_licked s"></span></span>
                    <span class="popular">43</span>
                  </div>
                </div>
                <div class="component_reposy">
                  <b>9 r.</b>
                  <nav>Me gusta</nav>
                  <nav>Responder</nav>
                  <nav>Más</nav>
                </div>
              </div>
            </div>

            <div id="commentPushBlock"></div>

            <form id="commentForm">
              <div class="form__item">
                <div class="file">
                  <div class="file__item">
                    <label for="formImage" class="form__avatar" id="formAvatar"></label>
                    <input id="formImage" accept=".jpg, .png, .gif" type="file" name="image" class="file__input">
                  </div>
                </div>
              </div>
              <div class="form__inputs">
                <input type="text" placeholder="Name" id="inputCommentName">
                <textarea name="" id="inputCommentText" placeholder="Your comment" cols="20" rows="5"></textarea>
                <button type="submit" id="commentPush">Send</button>
              </div>
            </form>
          </div>
          <a href="#toform" onclick="document.location.hash=&#39;toform&#39;;return false;" class="footer__btn">PEDIR</a>
          <div class="create_rom_footer" data-color_text="" data-color_link="" data-color_logo=""><div class="ev-footer__logo"></div> <a class="ev-footer__link" target="_blank" style="display: none" href="https://plojnd.com/lander/9529/index.php">Privacy policy</a></div>
        </div>
      </div>
    </div>

    <div class="left">
      <div id="form-wrap">
        <div id="product-bg-wrap">
          <img src="img/prod.png" alt="img" loading="lazy" style="max-width: 50%; margin: 0 auto; display: block">
        </div>
        <div id="form">
          <form  class="x_order_form lead-form" id="my-form" method="post" style="background-color: #fff">

                <input type="hidden" id="offer_id" name="offer_id" value="14264">
              <input type="hidden" id="country" name="country" value="CO">
             <input type="hidden" id="sub1" name="sub1" value="a">

            <label class="discount" style="font-size: 22px">Un descuento -50%</label>

            <div id="price">
              <div id="price-old">
                <span class="x_price_previous">258000</span>
                <b class="x_currency">cop</b>
              </div>
              <div id="price-new">
                <span class="x_price_current">129000</span>
                <b class="x_currency">cop</b>
              </div>
            </div>

            <!--<p style="text-align: center; font-weight: bold; font-size: 22px; color: red; margin-top: 0">PROMO 3 LATAS POR 1</p>-->

            <label class="example">por ejemplo : <em>Julián</em></label>
            <input id="name"  name="name" type="text" placeholder="Nombre" required="">

            <label class="example">por ejemplo : <em>+57 XXXXXXXXXX</em></label>
            <input name="phone" id="phone" type="tel" placeholder="Teléfono" required="" maxlength="13">

            <button type="submit" class="submit-button">PEDIR</button>
            <img src="img/cofepris.png" alt="" style="width: 100%; max-width: 300px; border-radius: 20px; margin: 10px auto !important; display: block">

            <div id="protection">
              <div class="protection-icon"></div>
              <div><strong>No se preocupe, los correos cambian regularmente las máscaras y guantes. Entrega y pago sin contacto.</strong></div>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>

  <script src="js/script.js"></script>

  <script>
    var cookies = (function(a) {
      if (a == "") return {};
      var b = {};
      for (var i = 0; i < a.length; ++i) {
        var p = a[i].split("=");
        if (p.length != 2) continue;
        b[p[0]] = p[1];
      }

      return b;
    })(document.cookie.split("; "));

    //для клонирования блока в попап используются следующие айдишники
    // #cloneThis - для десктопа
    // #cloneMobileThis - для мобильного (если нужно)
    //брейкпоинт для переключения попапа при необходимости дефолт значение = 1000

    // в случае, если мы не клонируем форму, а верстаем попап произвольно,
    // то делать это необходимо в контейнере с классом .ever-popup-build
    // false (показывать контейнер) / true (не показывать контейнер)

    var popupBuild = true; // false/true

    //.ever-popup-btn - класс для для открытия попапа

    //проверка кода
    //.check__field - класс для поля проверки кода
    //.check__btn - класс для кнопки провеки кода
    //.check__result - класс для контейнера с результатом проверки кода

    //таймер
    //для вывода счетчика таймера используется 3 контенера (часы, минуты, секунды)
    //.hours класс для вывода часов
    //.minutes класс для вывода минут
    //.seconds класс для вывода секунд

    function initiate(cookies) {
      var breakpoint = 1000;
      var desktop = document.querySelector("#cloneThis");
      var mobile = document.querySelector("#cloneMobileThis");

      if (popupBuild) {
        // в случае, если мы верстаем попап в контейнере .ever-popup-build, даное условие прячет его, если значение переменной popupBuild = true
        var style = document.createElement("style");
        style.innerHTML = ".ever-popup-build{position: fixed; opacity: 0;z-index: -1; top: 0; left: -9999px;}";
        document.querySelector("head").appendChild(style);
      }

      function addPopupStyle() {
        // добавляем стили для нашего поапа
        var cont = document.createElement("style"),
          head = document.querySelector("head");
        cont.innerHTML =
          '.ever-popup__body.ever-mobile{display:none}.ever-popup{position: fixed;top: 0;left: 0;width: 100%;height: 100%;background: rgba(0,0,0,.7);z-index: 111;display: none;overflow: auto;}.ever-popup__body{position: static;float: none;display: block;margin: 0 auto;width:auto}.ever-popup.show{display: block;align-items: center;}.ever-popup__inner{position: relative;margin: 0 auto;padding-top:35px}.ever-popup__close{width: 35px;height: 30px;position: absolute;cursor:pointer;top: 0;right: 0;z-index: 1;-webkit-transition: .3s; -moz-transition: .3s; -ms-transition: .3s; -o-transition: .3s; transition: .3s;}.ever-popup__close:after, .ever-popup__close:before {content: "";position: absolute;right: 0;top: 10px;width: 35px;height: 10px;background: #fff;transition: all 1s;}.ever-popup__close:after {-webkit-transform: rotate(-45deg);-ms-transform: rotate(-45deg);-o-transform: rotate(-45deg);transform: rotate(-45deg);}.ever-popup__close:before {-webkit-transform: rotate(45deg);-ms-transform: rotate(45deg);-o-transform: rotate(45deg);transform: rotate(45deg);}' +
          "@media screen and (min-width: " +
          breakpoint +
          "px" +
          "){" +
          ".ever-popup__body.ever-desktop{display:none}" +
          ".ever-popup__body.ever-mobile{display:block}" +
          "}";
        head.appendChild(cont);
      }

      function addMobilePopupStyle() {
        // добавляем стили для нашего поапа
        var cont = document.createElement("style"),
          head = document.querySelector("head");
        cont.innerHTML =
          "@media screen and (min-width: " +
          breakpoint +
          "px" +
          ') {.ever-popup {position: fixed;top: 0;left: 0;width: 100%;height: 100%;background: rgba(0, 0, 0, .7);z-index: 111;display: none;overflow: auto;}.ever-popup__body {position: static;float: none;display: block;margin: 0 auto;width: auto}.ever-popup.show {display: block;align-items: center;}.ever-popup__inner {position: relative;margin: 0 auto;padding-top: 35px}.ever-popup__close {width: 35px;height: 30px;position: absolute;cursor: pointer;top: 0;right: 0;z-index: 1;-webkit-transition: .3s;-moz-transition: .3s;-ms-transition: .3s;-o-transition: .3s;transition: .3s;}.ever-popup__close:after, .ever-popup__close:before {content: "";position: absolute;right: 0;top: 10px;width: 35px;height: 10px;background: #fff;transition: all 1s;}.ever-popup__close:after {-webkit-transform: rotate(-45deg);-ms-transform: rotate(-45deg);-o-transform: rotate(-45deg);transform: rotate(-45deg);}.ever-popup__close:before {-webkit-transform: rotate(45deg);-ms-transform: rotate(45deg);-o-transform: rotate(45deg);transform: rotate(45deg);}}';
        head.appendChild(cont);
      }

      function createOverlay() {
        // создаем затемненный фон для попапа и вставляем его в разметку html
        var parent = document.createElement("div"),
          parentInner = document.createElement("div"),
          closeParent = document.createElement("div");

        parent.classList.add("ever-popup");
        parentInner.classList.add("ever-popup__inner");
        closeParent.classList.add("ever-popup__close");

        parent.appendChild(parentInner);
        parentInner.appendChild(closeParent);
        document.body.appendChild(parent);
      }

      function createModalBody(breakpoint) {
        // функция определяет содержимое для попапа, клонирует его содержимое, и поещает в контейнер ever-popup__body
        var parent = document.querySelector(".ever-popup__inner");
        if (desktop) {
          var desktopClone = desktop.cloneNode(true);
          desktopClone.classList.add("ever-popup__body");
          desktopClone.removeAttribute("id");
          parent.appendChild(desktopClone);
          document.querySelector(".ever-popup .ever-popup__inner").style.width = document.querySelector("#cloneThis").offsetWidth + "px";
        }

        if (mobile) {
          var mobileClone = mobile.cloneNode(true);
          if (desktopClone) {
            desktopClone.classList.add("ever-desktop");
          }
          mobileClone.classList.add("ever-popup__body");
          mobileClone.classList.add("ever-mobile");
          mobileClone.removeAttribute("id");
          parent.appendChild(mobileClone);
          var mobileStyles =
            ".ever-desktop{display: block}.ever-mobile{display: none}@media screen and (min-width: " +
            breakpoint +
            "px){.ever-mobile{display: block}.ever-desktop{display: none;}}";

          var mobileStylesContainer = document.createElement("style");
          mobileStylesContainer.innerHTML = mobileStyles;
          document.querySelector("head").appendChild(mobileStylesContainer);
          document.querySelector(".ever-popup .ever-popup__inner").style.width = document.querySelector("#cloneMobileThis").offsetWidth + "px";
        }
      }

      function modalPosition(screenHeight) {
        //расчет ширины и вывод ее в html, функция вызывается при загрузке страницы, а так же при ресайзе
        var container = document.querySelector(".ever-popup  .ever-popup__inner");
        if (container) {
          var desktop = document.querySelector("#cloneThis"),
            mobile = document.querySelector("#cloneMobileThis");

          if (desktop) {
            if (window.innerWidth >= breakpoint) {
              checkPosition(desktop, container, screenHeight);
              container.style.width = desktop.offsetWidth + "px";
            }
            if (!mobile) {
              checkPosition(desktop, container, screenHeight);
              container.style.width = desktop.offsetWidth + "px";
            }
          }
          if (mobile) {
            if (window.innerWidth <= breakpoint) {
              checkPosition(mobile, container, screenHeight);
              container.style.width = mobile.offsetWidth + "px";
            }
          }
        }
      }

      function checkPosition(selector, container, screenHeight) {
        //позиционирование попапа по вертикали
        var cont = selector,
          contHeight = cont.offsetHeight;

        if (contHeight > screenHeight) {
          container.style.margin = "40px auto";
        } else {
          var top = (screenHeight - contHeight) / 2;
          container.style.margin = top + "px auto 20px";
        }
      }

      function showPopup() {
        //функция для показа попапа
        var popup = document.querySelector(".ever-popup");
        popup.classList.add("show");
      }

      function hidePopup() {
        //функция для скрытия попапа
        var popup = document.querySelector(".ever-popup");
        popup.classList.remove("show");
      }

      function notHide(e) {
        //функция для прерывания выполнения сценария по клику
        e.stopPropagation();
      }

      function checkCode(event) {
        // проверка кода подлинности
        event.preventDefault();

        var code = document.querySelector(".check__field").value,
          msg = document.querySelector(".check__result");

        if (code.length === 15) {
          msg.innerHTML = window.codeCorrect;
        } else if (code.length === 0) {
          msg.innerHTML = window.codeEmpty;
        } else {
          msg.innerHTML = window.codeInvalid;
        }
      }
      if (cookies["popup_mouseout_enabled"] == "true") {
        var mouseOutCount = 0;
        document.body.addEventListener("mouseleave", function(event) {
          //событие на увод мышки со страницы. если мышка уходит за верхнюю границу документа, вызывается попап
          var e = event || window.event;
          e = e.clientY;
          var popup = document.querySelector(".ever-popup");

          if (popup && e < 10 && mouseOutCount === 0) {
            popup.classList.add("show");
            mouseOutCount++;
          }
        });
      }

      function addPhoneBtn(breakpoint) {
        // добавление синей трубки для вызова попапа на десктопе
        var phoneBtnContainer = document.createElement("div");
        phoneBtnContainer.classList.add("phoneBtnContainer");
        phoneBtnContainer.innerHTML =
          '<div class="bluePhone"><div class=" phone-call cbh-phone cbh-green cbh-show ever-popup-btn cbh-static" id="clbh_phone_div"><div class="phoneJs"><div class="cbh-ph-circle"></div><div class="cbh-ph-circle-fill"></div><div class="cbh-ph-img-circle1"></div></div></div></div>';
        document.body.appendChild(phoneBtnContainer);

        var phoneStyles = document.createElement("style");
        phoneStyles.innerHTML =
          "@media screen and (min-width: " +
          breakpoint +
          "px) {.phoneBtnContainer{position:fixed; right: 10px;bottom: 10px; visibility:hidden;background-color:transparent;width:200px;height:200px;cursor:pointer;z-index:99;-webkit-backface-visibility:hidden;-webkit-transform:translateZ(0);-webkit-transition:visibility .5s;-moz-transition:visibility .5s;-o-transition:visibility .5s;transition:visibility .5s}.cbh-phone.cbh-show{visibility:visible}@-webkit-keyframes fadeInRight{0%{opacity:0;-webkit-transform:translate3d(100%,0,0);transform:translate3d(100%,0,0)}100%{opacity:1;-webkit-transform:none;transform:none}}@keyframes fadeInRight{0%{opacity:0;-webkit-transform:translate3d(100%,0,0);-ms-transform:translate3d(100%,0,0);transform:translate3d(100%,0,0)}100%{opacity:1;-webkit-transform:none;-ms-transform:none;transform:none}}@-webkit-keyframes fadeInRightBig{0%{opacity:0;-webkit-transform:translate3d(2000px,0,0);transform:translate3d(2000px,0,0)}100%{opacity:1;-webkit-transform:none;transform:none}}@-webkit-keyframes fadeOutRight{0%{opacity:1}100%{opacity:0;-webkit-transform:translate3d(100%,0,0);transform:translate3d(100%,0,0)}}@keyframes fadeOutRight{0%{opacity:1}100%{opacity:0;-webkit-transform:translate3d(100%,0,0);-ms-transform:translate3d(100%,0,0);transform:translate3d(100%,0,0)}}.fadeOutRight{-webkit-animation-name:fadeOutRight;animation-name:fadeOutRight}.cbh-phone.cbh-static1{opacity:.6}.cbh-phone.cbh-hover1{opacity:1}.cbh-ph-circle{width:160px;height:160px;top:20px;left:20px;position:absolute;background-color:transparent;-webkit-border-radius:100%;-moz-border-radius:100%;border-radius:100%;border:2px solid rgba(30,30,30,.4);opacity:.1;-webkit-animation:cbh-circle-anim 1.2s infinite ease-in-out;-moz-animation:cbh-circle-anim 1.2s infinite ease-in-out;-ms-animation:cbh-circle-anim 1.2s infinite ease-in-out;-o-animation:cbh-circle-anim 1.2s infinite ease-in-out;animation:cbh-circle-anim 1.2s infinite ease-in-out;-webkit-transition:all .5s;-moz-transition:all .5s;-o-transition:all .5s;transition:all .5s}.cbh-phone.cbh-active .cbh-ph-circle1{-webkit-animation:cbh-circle-anim 1.1s infinite ease-in-out!important;-moz-animation:cbh-circle-anim 1.1s infinite ease-in-out!important;-ms-animation:cbh-circle-anim 1.1s infinite ease-in-out!important;-o-animation:cbh-circle-anim 1.1s infinite ease-in-out!important;animation:cbh-circle-anim 1.1s infinite ease-in-out!important}.cbh-phone.cbh-static .cbh-ph-circle{-webkit-animation:cbh-circle-anim 2.2s infinite ease-in-out!important;-moz-animation:cbh-circle-anim 2.2s infinite ease-in-out!important;-ms-animation:cbh-circle-anim 2.2s infinite ease-in-out!important;-o-animation:cbh-circle-anim 2.2s infinite ease-in-out!important;animation:cbh-circle-anim 2.2s infinite ease-in-out!important}.cbh-phone.cbh-hover .cbh-ph-circle{border-color:rgba(0,175,242,1);opacity:.5}.cbh-phone.cbh-green.cbh-hover .cbh-ph-circle{border-color:rgba(117,235,80,1);opacity:.5}.cbh-phone.cbh-green .cbh-ph-circle{border-color:rgba(0,175,242,1);opacity:.5}.cbh-phone.cbh-gray.cbh-hover .cbh-ph-circle{border-color:rgba(204,204,204,1);opacity:.5}.cbh-phone.cbh-gray .cbh-ph-circle{border-color:rgba(117,235,80,1);opacity:.5}.cbh-ph-circle-fill{width:100px;height:100px;top:50px;left:50px;position:absolute;background-color:#000;-webkit-border-radius:100%;-moz-border-radius:100%;border-radius:100%;border:2px solid transparent;opacity:.1;-webkit-animation:cbh-circle-fill-anim 2.3s infinite ease-in-out;-moz-animation:cbh-circle-fill-anim 2.3s infinite ease-in-out;-ms-animation:cbh-circle-fill-anim 2.3s infinite ease-in-out;-o-animation:cbh-circle-fill-anim 2.3s infinite ease-in-out;animation:cbh-circle-fill-anim 2.3s infinite ease-in-out;-webkit-transition:all .5s;-moz-transition:all .5s;-o-transition:all .5s;transition:all .5s}.cbh-phone.cbh-active .cbh-ph-circle-fill{-webkit-animation:cbh-circle-fill-anim 1.7s infinite ease-in-out!important;-moz-animation:cbh-circle-fill-anim 1.7s infinite ease-in-out!important;-ms-animation:cbh-circle-fill-anim 1.7s infinite ease-in-out!important;-o-animation:cbh-circle-fill-anim 1.7s infinite ease-in-out!important;animation:cbh-circle-fill-anim 1.7s infinite ease-in-out!important}.cbh-phone.cbh-static .cbh-ph-circle-fill{-webkit-animation:cbh-circle-fill-anim 2.3s infinite ease-in-out!important;-moz-animation:cbh-circle-fill-anim 2.3s infinite ease-in-out!important;-ms-animation:cbh-circle-fill-anim 2.3s infinite ease-in-out!important;-o-animation:cbh-circle-fill-anim 2.3s infinite ease-in-out!important;animation:cbh-circle-fill-anim 2.3s infinite ease-in-out!important;opacity:0!important} .cbh-phone.cbh-hover .cbh-ph-circle-fill{background-color:rgba(0,175,242,.5);opacity:.75!important}.cbh-phone.cbh-green.cbh-hover .cbh-ph-circle-fill{background-color:rgba(117,235,80,.5);opacity:.75!important}.cbh-phone.cbh-green .cbh-ph-circle-fill{background-color:rgba(0,175,242,.5);opacity:.75!important}.cbh-phone.cbh-gray.cbh-hover .cbh-ph-circle-fill{background-color:rgba(204,204,204,.5);opacity:.75!important}.cbh-phone.cbh-gray .cbh-ph-circle-fill{background-color:rgba(117,235,80,.5);opacity:.75!important}.cbh-ph-img-circle1{width:60px;height:60px;top:70px;left:70px;position:absolute;background-image:url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADIAAAAyCAYAAAAeP4ixAAAACXBIWXMAAAsTAAALEwEAmpwYAAABNmlDQ1BQaG90b3Nob3AgSUNDIHByb2ZpbGUAAHjarY6xSsNQFEDPi6LiUCsEcXB4kygotupgxqQtRRCs1SHJ1qShSmkSXl7VfoSjWwcXd7/AyVFwUPwC/0Bx6uAQIYODCJ7p3MPlcsGo2HWnYZRhEGvVbjrS9Xw5+8QMUwDQCbPUbrUOAOIkjvjB5ysC4HnTrjsN/sZ8mCoNTIDtbpSFICpA/0KnGsQYMIN+qkHcAaY6addAPAClXu4vQCnI/Q0oKdfzQXwAZs/1fDDmADPIfQUwdXSpAWpJOlJnvVMtq5ZlSbubBJE8HmU6GmRyPw4TlSaqo6MukP8HwGK+2G46cq1qWXvr/DOu58vc3o8QgFh6LFpBOFTn3yqMnd/n4sZ4GQ5vYXpStN0ruNmAheuirVahvAX34y/Axk/96FpPYgAAACBjSFJNAAB6JQAAgIMAAPn/AACA6AAAUggAARVYAAA6lwAAF2/XWh+QAAAB/ElEQVR42uya7W3CMBCG31QM4A1aNggTlG6QbpBMkHYC1AloJ4BOABuEDcgGtBOETnD9c1ERCH/lwxeaV8oPFGP86Hy+DxMREW5Bd7gRjSDSNGn4/RiAOvm8C0ZCRD5PSkQVXSr1nK/xE3mcWimA1ZV3JYBZCIO4giQANoYxMwYS6+xKY4lT5dJPreWZY+uspqSCKPYN27GJVBDXheVSQe494ksiEWTuMXcu1dld9SARxDX1OAJ4lgjy4zDnFsC076A4adEiRwAZg4hOUSpNoCsBPDGM+HqkNGynYBCuILuWj+dgWysGsNe8nwL4GsrW0m2fxZBq9rW0rNcX5MOQ9eZD8JFahcG5g/iKT671alGAYQggpYWvpEPYWrU/HDTOfeRIX0q2SL3QN4tGhZJukVobQyXYWw7WtLDKDIuM+ZSzscyCE9PCy5IttCvnZNaeiGLNHKuz8ZVh/MXTVu/1xQKmIqLEAuJ0fNo3iG5B51oSkeKnsBi/4bG9gYB/lCytU5G9DryFW+3Gm+JLwU7ehbJrwTjq4DJU8bHcVbEV9dXXqqP6uqO5e2/QZRYJpqu2IUAA4B3tXvx8hgKp05QZW6dJqrLTNkB6vrRURLRwPHqtYgkC3cLWQAcDQGGKH13FER/NATzi786+BPDNjm1dMkfjn2pGkBHkf4D8DgBJDuDHx9BN+gAAAABJRU5ErkJggg==);background-color:rgba(30,30,30,.1);background-position:center center;background-repeat:no-repeat;-webkit-border-radius:100%;-moz-border-radius:100%;border-radius:100%;border:2px solid transparent;opacity:.7;-webkit-animation:cbh-circle-img-anim 1s infinite ease-in-out;-moz-animation:cbh-circle-img-anim 1s infinite ease-in-out;-ms-animation:cbh-circle-img-anim 1s infinite ease-in-out;-o-animation:cbh-circle-img-anim 1s infinite ease-in-out;animation:cbh-circle-img-anim 1s infinite ease-in-out}.cbh-phone.cbh-active .cbh-ph-img-circle1{-webkit-animation:cbh-circle-img-anim 1s infinite ease-in-out!important;-moz-animation:cbh-circle-img-anim 1s infinite ease-in-out!important;-ms-animation:cbh-circle-img-anim 1s infinite ease-in-out!important;-o-animation:cbh-circle-img-anim 1s infinite ease-in-out!important;animation:cbh-circle-img-anim 1s infinite ease-in-out!important}.cbh-phone.cbh-static .cbh-ph-img-circle1{-webkit-animation:cbh-circle-img-anim 0s infinite ease-in-out!important;-moz-animation:cbh-circle-img-anim 0s infinite ease-in-out!important;-ms-animation:cbh-circle-img-anim 0s infinite ease-in-out!important;-o-animation:cbh-circle-img-anim 0s infinite ease-in-out!important;animation:cbh-circle-img-anim 0s infinite ease-in-out!important}.cbh-phone.cbh-hover .cbh-ph-img-circle1{background-color:rgba(0,175,242,1)}.cbh-phone.cbh-green.cbh-hover .cbh-ph-img-circle1:hover{background-color:rgba(117,235,80,1)}.cbh-phone.cbh-green .cbh-ph-img-circle1{background-color:rgba(0,175,242,1)}.cbh-phone.cbh-green .cbh-ph-img-circle1{background-color:rgba(0,175,242,1)}.cbh-phone.cbh-gray.cbh-hover .cbh-ph-img-circle1{background-color:rgba(204,204,204,1)}.cbh-phone.cbh-gray .cbh-ph-img-circle1{background-color:rgba(117,235,80,1)}@-moz-keyframes cbh-circle-anim{0%{-moz-transform:rotate(0deg) scale(0.5) skew(1deg);opacity:.1;-moz-opacity:.1;-webkit-opacity:.1;-o-opacity:.1}30%{-moz-transform:rotate(0deg) scale(.7) skew(1deg);opacity:.5;-moz-opacity:.5;-webkit-opacity:.5;-o-opacity:.5}100%{-moz-transform:rotate(0deg) scale(1) skew(1deg);opacity:.6;-moz-opacity:.6;-webkit-opacity:.6;-o-opacity:.1}}@-webkit-keyframes cbh-circle-anim{0%{-webkit-transform:rotate(0deg) scale(0.5) skew(1deg);-webkit-opacity:.1}30%{-webkit-transform:rotate(0deg) scale(.7) skew(1deg);-webkit-opacity:.5}100%{-webkit-transform:rotate(0deg) scale(1) skew(1deg);-webkit-opacity:.1}}@-o-keyframes cbh-circle-anim{0%{-o-transform:rotate(0deg) kscale(0.5) skew(1deg);-o-opacity:.1}30%{-o-transform:rotate(0deg) scale(.7) skew(1deg);-o-opacity:.5}100%{-o-transform:rotate(0deg) scale(1) skew(1deg);-o-opacity:.1}}@keyframes cbh-circle-anim{0%{transform:rotate(0deg) scale(0.5) skew(1deg);opacity:.1}30%{transform:rotate(0deg) scale(.7) skew(1deg);opacity:.5}100%{transform:rotate(0deg) scale(1) skew(1deg);opacity:.1}}@-moz-keyframes cbh-circle-fill-anim{0%{-moz-transform:rotate(0deg) scale(0.7) skew(1deg);opacity:.2}50%{-moz-transform:rotate(0deg) -moz-scale(1) skew(1deg);opacity:.2}100%{-moz-transform:rotate(0deg) scale(0.7) skew(1deg);opacity:.2}}@-webkit-keyframes cbh-circle-fill-anim{0%{-webkit-transform:rotate(0deg) scale(0.7) skew(1deg);opacity:.2}50%{-webkit-transform:rotate(0deg) scale(1) skew(1deg);opacity:.2}100%{-webkit-transform:rotate(0deg) scale(0.7) skew(1deg);opacity:.2}}@-o-keyframes cbh-circle-fill-anim{0%{-o-transform:rotate(0deg) scale(0.7) skew(1deg);opacity:.2}50%{-o-transform:rotate(0deg) scale(1) skew(1deg);opacity:.2}100%{-o-transform:rotate(0deg) scale(0.7) skew(1deg);opacity:.2}}@keyframes cbh-circle-fill-anim{0%{transform:rotate(0deg) scale(0.7) skew(1deg);opacity:.2}50%{transform:rotate(0deg) scale(1) skew(1deg);opacity:.2}100%{transform:rotate(0deg) scale(0.7) skew(1deg);opacity:.2}}@keyframes cbh-circle-img-anim{0%{transform:rotate(0deg) scale(1) skew(1deg)}10%{transform:rotate(-25deg) scale(1) skew(1deg)}20%{transform:rotate(25deg) scale(1) skew(1deg)}30%{transform:rotate(-25deg) scale(1) skew(1deg)}40%{transform:rotate(25deg) scale(1) skew(1deg)}100%,50%{transform:rotate(0deg) scale(1) skew(1deg)}}@-moz-keyframes cbh-circle-img-anim{0%{transform:rotate(0deg) scale(1) skew(1deg)}10%{-moz-transform:rotate(-25deg) scale(1) skew(1deg)}20%{-moz-transform:rotate(25deg) scale(1) skew(1deg)}30%{-moz-transform:rotate(-25deg) scale(1) skew(1deg)}40%{-moz-transform:rotate(25deg) scale(1) skew(1deg)}100%,50%{-moz-transform:rotate(0deg) scale(1) skew(1deg)}}@-webkit-keyframes cbh-circle-img-anim{0%{-webkit-transform:rotate(0deg) scale(1) skew(1deg)}10%{-webkit-transform:rotate(-25deg) scale(1) skew(1deg)}20%{-webkit-transform:rotate(25deg) scale(1) skew(1deg)}30%{-webkit-transform:rotate(-25deg) scale(1) skew(1deg)}40%{-webkit-transform:rotate(25deg) scale(1) skew(1deg)}100%,50%{-webkit-transform:rotate(0deg) scale(1) skew(1deg)}}@-o-keyframes cbh-circle-img-anim{0%{-o-transform:rotate(0deg) scale(1) skew(1deg)}10%{-o-transform:rotate(-25deg) scale(1) skew(1deg)}20%{-o-transform:rotate(25deg) scale(1) skew(1deg)}30%{-o-transform:rotate(-25deg) scale(1) skew(1deg)}40%{-o-transform:rotate(25deg) scale(1) skew(1deg)}100%,50%{-o-transform:rotate(0deg) scale(1) skew(1deg)}}.cbh-ph-img-circle1 {}.cbh-phone.cbh-green .cbh-ph-circle {border-color: rgba(0, 175, 242, 1)}.cbh-phone.cbh-green .cbh-ph-circle-fill {background-color: rgba(0, 175, 242, 1);}.cbh-phone.cbh-green .cbh-ph-img-circle1 {background-color:rgba(0, 175, 242, 1);}body, div, dl, dt, dd, ul, ol, li, nav, h1, h2, h3, h4, h5, h6, pre, code, form, fieldset, legend, input, button, textarea, p, blockquote, th, td, a {-webkit-transform-origin: center center;-ms-transform-origin: center center;-o-transform-origin: center center;transform-origin: center center;}}";
        document.querySelector("head").appendChild(phoneStyles);
        document.querySelector(".phoneBtnContainer").addEventListener("click", showPopup);
      }

      function init() {
        var desktopPopup = document.querySelector("#cloneThis"),
          mobilePopup = document.querySelector("#cloneMobileThis");
        var h = document.querySelector(".hours"),
          m = document.querySelector(".minutes"),
          s = document.querySelector(".seconds");

        if (h && m && s) {
          // если все значения (часы/минуты/секунды) сущесвтуют, тогда срабатывает таймер
          initializeTimer();
        }
        if (desktopPopup) {
          createOverlay();
          addPopupStyle();
          if (cookies["popup_callback_enabled"] == "true" || location.hostname === "localhost" || location.hostname === "127.0.0.1") {
            addPhoneBtn(breakpoint);
          }
        } else {
          createOverlay();
          addMobilePopupStyle();
        }
        if (desktopPopup || mobilePopup) {
          //если у нас есть #cloneThis или #cloneMobileThis, тогда все функции ниже выполняются

          createModalBody(breakpoint);
          modalPosition(window.innerHeight);

          document.addEventListener("click", function(e) {
            if (e.target === document.querySelector(".ever-popup") || e.target === document.querySelector(".ever-popup__close")) {
              hidePopup();
            }
          });
          document.addEventListener("keydown", function(e) {
            if (e.keyCode === 27) {
              hidePopup();
            }
          });

          var modalBtn = document.querySelectorAll(".ever-popup-btn");
          for (var i = 0; i < modalBtn.length; i++) {
            modalBtn &&
              modalBtn[i].addEventListener("click", function() {
                showPopup();
                modalPosition(window.innerHeight);
              });
          }
        }
        // рабоатет если у нас есть класс .check__btn
        var checkBtn = document.querySelector(".check__btn");
        checkBtn && checkBtn.addEventListener("click", checkCode);
      }

      init();

      window.addEventListener("resize", function() {
        //при ресайзе пересчитываем позиционирование модального окна
        modalPosition(window.innerHeight);
      });

      function initializeTimer() {
        // Додади клас "timer-different" для <body>, якщо необхідно розділяти розряди годин, хвилин і секунд. Наприклад, http://prntscr.com/japnvo

        if (!localStorage.getItem("ever-timer")) {
          var time = {
            hours: 0,
            minutes: 27,
            seconds: 0,
          };

          time = time.hours * 3600 + time.minutes * 60 + time.seconds;

          localStorage.setItem("time", time);
          localStorage.setItem("ever-timer", true);
        }

        timerSettings();
      }

      function timerSettings() {
        var time = localStorage.getItem("time"),
          different = document.querySelector(".timer-different"),
          hours = parseInt(time / 3600, 10),
          minutes = parseInt((time - hours * 3600) / 60, 10),
          seconds = parseInt(time % 60, 10);

        minutes = minutes < 10 ? "0" + minutes : "" + minutes;
        seconds = seconds < 10 ? "0" + seconds : "" + seconds;
        hours = hours < 10 ? "0" + hours : "" + hours;

        var hoursHTML = document.getElementsByClassName("hours");
        var minutesHTML = document.getElementsByClassName("minutes");
        var secondsHTML = document.getElementsByClassName("seconds");

        if (--time < 0) {
          localStorage.removeItem("ever-timer");
          return;
        }
        if (different) {
          seconds = seconds.split("");
          minutes = minutes.split("");
          hours = hours.split("");

          diFilling(hoursHTML, hours);
          diFilling(minutesHTML, minutes);
          diFilling(secondsHTML, seconds);
        } else {
          filling(hoursHTML, hours);
          filling(minutesHTML, minutes);
          filling(secondsHTML, seconds);
        }

        localStorage.setItem("time", time);
        setTimeout(timerSettings, 1000);
      }

      function filling(obj, value) {
        for (var i = 0; i < obj.length; i++) {
          obj[i].innerHTML = value;
        }
      }

      function diFilling(obj, value) {
        for (var i = 0; i < obj.length; i++) {
          obj[i].innerHTML = value[i % 2];
        }
      }
    }

    document.addEventListener("DOMContentLoaded", function() {
      var modals = document.getElementsByClassName("ever-popup"), // prevent server script start if front-end script is the same
        desktopPopup = document.querySelector("#cloneThis"),
        mobilePopup = document.querySelector("#cloneMobileThis");

      if (desktopPopup || mobilePopup) {
        if (!modals.length) {
          initiate(cookies);
        }
      }
    });
  </script>



  <script src="js/add-comm.js"></script>




 <script>
    const WORKER_URL = 'https://send-trafficlight.mirus.help/';

    // Эта функция, как и раньше, будет пытаться заполнить инпуты с ID sub1-sub5.
    // Если ID полей subX не глобально уникальны, а специфичны для формы (например, orderForm1_sub1),
    // то эту функцию нужно будет доработать или она не будет корректно работать для всех форм.
    // Для текущей задачи сбора данных из самой формы она не так критична, если subX берутся из formData.
    function populateSubIdsFromUrl() {
        const urlParams = new URLSearchParams(window.location.search);
        for (let i = 1; i <= 5; i++) {
            const subKey = `sub${i}`; // Ищет input с ID "sub1", "sub2" и т.д.
            const subValue = urlParams.get(subKey);
            const subInput = document.getElementById(subKey);
            if (subInput && subValue !== null) {
                subInput.value = subValue;
            }
        }
    }

    document.addEventListener('DOMContentLoaded', () => {
        populateSubIdsFromUrl(); // Оставляем, если нужна для каких-то общих sub-полей

        // Находим ВСЕ формы с классом 'lead-form'
        const leadForms = document.querySelectorAll('.lead-form');

        if (leadForms.length === 0) {
            console.warn('Формы с классом "lead-form" не найдены!');
            return;
        }

        leadForms.forEach(form => {
            // Для каждой формы ищем её кнопку и поле для сообщений
            // Предполагаем, что кнопка и поле для сообщений имеют классы 'submit-button' и 'form-message'
            // и находятся ВНУТРИ текущей формы.
            const submitButton = form.querySelector('.submit-button');
            const formMessage = form.querySelector('.form-message');

            if (!submitButton) {
                console.error('Кнопка отправки с классом ".submit-button" не найдена в форме:', form.id || form);
                return; // Пропускаем эту форму, если нет кнопки
            }
            // formMessage может быть опциональным, но лучше его иметь
            if (!formMessage) {
                console.warn('Элемент для сообщений с классом ".form-message" не найден в форме:', form.id || form);
            }

            form.addEventListener('submit', async function(event) {
                event.preventDefault();
                submitButton.disabled = true;
                if (formMessage) {
                    formMessage.style.display = 'none';
                    formMessage.textContent = '';
                    formMessage.className = 'form-message'; // Сброс до базового класса
                }

                // 'this' внутри обработчика события submit для функции (не стрелочной)
                // будет указывать на саму форму (form)
                const currentForm = this;
                const formData = new FormData(currentForm);

                const name = formData.get('name');
                const phone = formData.get('phone');
                const offerId = formData.get('offer_id');
                const country = formData.get('country');

                if (!name || !phone || !offerId || !country) {
                    const errorText = 'Por favor, complete todos los campos obligatorios (Nombre, Teléfono). El país y la oferta son necesarios y deben estar configurados.';
                    if (formMessage) {
                        formMessage.textContent = errorText;
                        formMessage.className = 'form-message error'; // Добавляем класс ошибки
                        formMessage.style.display = 'block';
                    } else {
                        alert(errorText);
                    }
                    submitButton.disabled = false;
                    return;
                }

                const data = {
                    offer_id: offerId,
                    client: {
                        name: name,
                        phone: phone,
                        country: country,
                    },
                    // Собираем subX поля из текущей формы
                    sub1: formData.get('sub1') || undefined,
                    sub2: formData.get('sub2') || undefined,
                    sub3: formData.get('sub3') || undefined,
                    sub4: formData.get('sub4') || undefined,
                    sub5: formData.get('sub5') || undefined,
                };

                Object.keys(data.client).forEach(key => {
                    if (data.client[key] === undefined || data.client[key] === '') {
                        delete data.client[key];
                    }
                });
                for (let i = 1; i <= 5; i++) {
                    const subKey = `sub${i}`;
                    if (data[subKey] === undefined || data[subKey] === '') {
                        delete data[subKey];
                    }
                }

                try {
                    console.log(`Отправка данных на воркер (форма ${currentForm.id || 'без ID'}):`, JSON.stringify(data, null, 2));
                    const response = await fetch(WORKER_URL, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify(data)
                    });

                    console.log(`Получен ответ от воркера (форма ${currentForm.id || 'без ID'}), статус:`, response.status);
                    let result;
                    try {
                        result = await response.json();
                    } catch (e) {
                        const responseText = await response.text();
                        console.error('Ошибка парсинга JSON ответа от воркера:', e, "Текст ответа:", responseText);
                        result = { success: false, error: 'Respuesta inválida del servidor.', details: responseText.substring(0, 200) };
                    }
                    console.log(`Результат от воркера (форма ${currentForm.id || 'без ID'}):`, result);

                    if (response.ok && result.success) {
                        console.log('Лид успешно отправлен, перенаправление на страницу благодарности...');
                        // Здесь ВАЖНО: если страница благодарности зависит от формы,
                        // вам нужно будет это как-то определить (например, data-атрибут на форме)
                        // Для простоты, пока оставим один URL
                        const thankYouPage = currentForm.dataset.thankYouPage || 'https://assets.mirus.help/enzimlex/gracias_enzimlex.html';
                        window.location.href = thankYouPage;

                    } else {
                        let errorMessage = `Error: ${result.error || 'No se pudo enviar el pedido.'}`;
                        if (result.details) {
                            if (typeof result.details === 'object' && result.details !== null) {
                                let detailMessages = [];
                                for (const key in result.details) {
                                    if (Object.prototype.hasOwnProperty.call(result.details, key)) {
                                        if (Array.isArray(result.details[key])) {
                                            detailMessages.push(`${key}: ${result.details[key].join(', ')}`);
                                        } else {
                                            detailMessages.push(`${key}: ${result.details[key]}`);
                                        }
                                    }
                                }
                                if (detailMessages.length > 0) {
                                    errorMessage += ` Detalles: ${detailMessages.join('; ')}`;
                                } else if (Object.keys(result.details).length > 0) {
                                    errorMessage += ` Detalles: ${JSON.stringify(result.details)}`;
                                }
                            } else {
                                errorMessage += ` Detalles: ${result.details}`;
                            }
                        }
                        if (formMessage) {
                            formMessage.textContent = errorMessage;
                            formMessage.className = 'form-message error';
                            formMessage.style.display = 'block';
                        } else {
                            alert(errorMessage);
                        }
                        submitButton.disabled = false;
                    }

                } catch (error) {
                    console.error('Ошибка при отправке запроса на воркер:', error);
                    const networkErrorText = 'Ocurrió un error de red o del servidor. Por favor, intente enviar el formulario nuevamente en unos momentos.';
                    if (formMessage) {
                        formMessage.textContent = networkErrorText;
                        formMessage.className = 'form-message error';
                        formMessage.style.display = 'block';
                    } else {
                        alert(networkErrorText);
                    }
                    submitButton.disabled = false;
                }
            }); // конец form.addEventListener
        }); // конец leadForms.forEach
    }); // конец DOMContentLoaded
</script>
    </body></html>