<!DOCTYPE html><html lang="es" dir="ltr"><head><link rel="preconnect" href="//rocket-commander-prod.b-cdn.net/">

	<meta charset="UTF-8">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<title>Virex</title>
	<link rel="stylesheet" href="css/7OIyqwGwZBEJ.css">
	<link rel="icon" href="images/5IPXSfamODf2.ico" type="image/x-icon">

</head>

<body>

	<div class=" wrapper">
		<header class="b-header">
			<div class="header-top d-large">
				<div class="container">
					<div class="top-menu">
						<a href="#toform"> Inicio </a><a href="#toform"> BLOG </a><a href="#toform">
							GALERÍA de articulos </a><a href="#toform"> ARCHIVO </a><a href="#toform"> Proyectos
							especiales </a><a href="#toform"> CONTACTO </a><a href="#toform"> FUNCIONES </a></div>
					<div class="b-social">
						<a class="fb" href="#toform"></a>
						<a class="in" href="#toform"></a>
						<a class="pn" href="#toform"></a>
						<a class="tw" href="#toform"></a>
					</div>
				</div>
			</div>
			<div class="header-middle">
				<div class="container"><a class="b-logo" href="#toform"><span class="logo-red"> stream.com </span><span class="logo-blue"> Noticias de última hora </span><span class="logo-black"> En directo
						</span><span class="logo-white"> últimas noticias de países y del mundo </span></a>
				
					<div class="b-search d-mobile">

					</div>
				</div>
			</div>
			<div class="header-bottom d-large">
				<div class="container">
					<div class="bottom-menu"><a href="#toform"> NOTICIAS </a><a href="#toform"> TECNOLOGÍA </a><a href="#toform"> POLÍTICA
						</a><a href="#toform"> ECONOMÍA </a><a href="#toform"> ACCIDENTES
						</a><a class="current" href="#toform"> medicina </a><a href="#toform"> SOCIEDAD </a><a href="#toform"> DEPORTE
						</a><a href="#toform"> CULTURA </a><a href="#toform"> CIENCIAS
						</a><a href="#toform"> COCHES </a></div>
				</div>
			</div>
		</header>
		<section class="b-primary">
			<div class="container">
				<aside class="b-sidebar d-large"><a class="b-search2" href="#toform"><span> Buscar </span></a>
					<div class="b-side">
						<div class="title-block"> ÚLTIMAS NOTICIAS </div>
						<div class="b-post">
							<div class="post-img">
								<div class="post-num"> 1 </div><img loading="lazy" src="images/LqVhOZuxyQk6.jpg" alt="">
							</div>
							<div class="teaser"> Un estudio realizado por la Revista de Medicina Sexual dice que...
							</div>
							<div class="post-info">
								<span></span>
								<span></span>
							</div>
							<a class="post-link" href="#toform"></a>
						</div>
						<div class="b-post">
							<div class="post-img">
								<div class="post-num"> 2 </div><img loading="lazy" src="images/UXRftCqeUdMs.jpg" alt="">
							</div>
							<div class="teaser"> Las estadísticas van en aumento. 4685 pacientes infectados con
								coronavirus por día </div>
							<div class="post-info">
								<span></span>
								<span></span>
							</div>
							<a class="post-link" href="#toform"></a>
						</div>
						<div class="b-post">
							<div class="post-img">
								<div class="post-num"> 3 </div><img loading="lazy" src="images/t7No3OEPG8VD.jpg" alt="">
							</div>
							<div class="teaser"> 9 ideas románticas de vacaciones que recordarás para siempre </div>
							<div class="post-info">
								<span></span>
								<span></span>
							</div>
							<a class="post-link" href="#toform"></a>
						</div>
						<div class="b-post post-last">
							<div class="post-img">
								<div class="post-num"> 4 </div><img loading="lazy" src="images/gYi4lyMRdF5E.jpg" alt="">
							</div>
							<div class="teaser"> Terapia de postres: 10 increíbles delicias de los restaurantes
								metropolitanos para disipar la tristeza del otoño </div>
							<div class="post-info">
								<span></span>
								<span></span>
							</div>
							<a class="post-link" href="#toform"></a>
						</div><a class="all-news" href="#toform"> TODAS LAS NOTICIAS </a>
					</div>
					<div class="b-side text-center"> Síguenos en las redes sociales
						<div class="left-social">
							<a class="fb" href="#toform"></a>
							<a class="in" href="#toform"></a>
							<a class="pn" href="#toform"></a>
							<a class="tw" href="#toform"></a>
						</div>
					</div>
					<div class="bnr-block"><img loading="lazy" class="bnr-img" src="images/6H4E4tVePrl4.jpg" alt="">
						<div class="bnr-text text-uppercase"> Complejo <span class="prod-name text-uppercase"> Virex
							</span><span> una alternativa a la Viagra </span><a class="btn" href="#toform"> Pedir </a>
						</div><img style="width: 250px;" loading="lazy" class="bnr-img2" src="images/UlbutaKR7HcY.png" alt="">
					</div>
				</aside>
				<article class="content">
					<div class="main-content">
						<div class="b-tags"><span class="text-uppercase">Noticias de última hora </span>
							<div class="breadcrumbs"><a href="#toform"> Inicio </a> / <a href="#toform"> Proyectos
									especiales </a> / Medicina </div>
						</div>
						<h1> Ha aparecido en el mercado un análogo seguro de la Viagra </h1>
						<h2> El nuevo producto devuelve la fuerza masculina sin dañar el corazón ni los vasos sanguíneos
						</h2>
						<div class="article-info">
							<div>
								<span><br></span>
								<span></span>
							</div>
							<div class="b-social"><span class="d-large"> COMPARTIR </span>
								<a class="fb" href="#toform"></a>
								<a class="in" href="#toform"></a>
								<a class="pn" href="#toform"></a>
								<a class="tw" href="#toform"></a>
							</div>
						</div>
						<p> Investigadores estadounidenses han inventado un producto gracias al cual miles de hombres
							pronto se olvidarán de la prostatitis y la disfunción eréctil. <strong> El nuevo complejo de
								suplementos dietéticos es 3 veces más eficaz que el sildenafil (Viagra) y no afecta a la
								función cardíaca. </strong></p>
						<picture>
							<source srcset="images/oC0hNj2vZxNc.webp" type="image/webp">
							<img loading="lazy" class="img-responsive d-center" src="images/NLgvkn8whmuo.jpg" alt="">
						</picture>
						<p> La fórmula de la ''nueva Viagra'' se basa en 17 principios activos, 11 de los cuales son
							extractos de plantas puros, 2 aminoácidos, 2 antioxidantes, biotina y quercetina. <strong>
								Según los desarrolladores, una composición tan rica salva al hombre de 7 problemas a la
								vez: </strong></p><img loading="lazy" class="img-responsive alignleft" src="images/d3i93Nm2YPaa.jpg" alt="">
						<ul class="simple-list">
							<li> disfunción eréctil </li>
							<li> prostatitis </li>
							<li> dolor al orinar </li>
							<li> eyaculación precoz </li>
							<li> baja libido </li>
							<li> deficiencia de testosterona </li>
							<li> infertilidad masculina </li>
						</ul>
						<hr class="separator">
						<h2> La ''nueva Viagra'' se llamó Virex. </h2>
						<p> Es un biocomplejo de gotas y tabletas de color azul brillante con ingredientes
							complementarios. Anteriormente, para combatir la prostatitis y los problemas de
							erección, el hombre se veía obligado a tragarse un puñados de pastillas (incluidas las
							hormonales). </p>
						<div class="text-center"></div>
						<hr class="separator">
						<div class="h2"> Virex funciona en 3 direcciones: </div><img loading="lazy" class="img-responsive alignleft" src="images/RvK7xkH3SnwZ.jpg" alt="">
						<ol class="num-list">
							<li> sanea el sistema genitourinario, aliviando el dolor y la inflamación </li>
							<li> mejora el suministro de sangre a la próstata y el pene </li>
							<li> estimula la producción de testosterona, la principal hormona masculina </li>
						</ol>
						<div class="clearfix"></div>
						<div class="b-stages">
							<ol>
								<li>
									<p><strong> En la primera etapa, los bioflavonoides contenidos en <a href="#toform">
												Virex </a> bloquean la actividad de las bacterias responsables de la
											prostatitis, desinfectan, alivian la inflamación, el dolor y la picazón en
											solo 2-3 horas. </strong> Las pruebas han demostrado que el insoportable
										dolor al orinar en pacientes con prostatitis desaparece por completo tras 7 días
										de ingesta diaria de Virex. </p>
									<div class="img-wrap"> Virex alivia la inflamación de la próstata
										<picture>
											<source srcset="images/z9zOtCiNtsis.webp" type="image/webp">
											<img loading="lazy" class="img-responsive" src="images/Sf7plEoSZAPG.jpg" alt="">
										</picture>
										<span> Antes
											de
											Virex </span><span> Después de Virex </span></div>
								</li>
								<li>
									<p><strong> En la segunda etapa, Virex actúa como el sildenafil (Viagra): acelera
											la
											síntesis de óxido nítrico en la sangre, relaja y dilata las arterias.
										</strong> La sangre llena los cuerpos esponjosos del pene y los músculos de la
										base del pene, y se producen erecciones, incluso en personas con disfunción
										eréctil. Pero a diferencia del sildenafil, Virex no actúa localmente en
										los vasos del pene (que es peligroso para el corazón), sino en todo el sistema
										genitourinario. Y cuanto más amplia sea el área de influencia, menor será
										la carga sobre el músculo cardíaco. Por lo tanto, Virex puede ser tomado
										de manera segura por hombres a partir de los 50 años, así como por quienes
										padecen enfermedades cardiovasculares. </p>
									<div class="img-wrap text-center"> Comparación de la erección con Viagra y Virex
										<picture>
											<source srcset="images/dRWNZMp8TMt4.webp" type="image/webp">
											<img loading="lazy" class="img-responsive" src="images/F2eiRSfGEjaz.jpg" alt="">
										</picture>
										<span> Al
											tomar
											Viagra </span><span> Al tomar Virex </span>
									</div>
								</li>
								<li>
									<p><strong> La tercera etapa es la mejora de los niveles hormonales. El
											extracto de la planta india Ashwagandha ''reinicia'' el trabajo de las
											glándulas endocrinas y aumenta la producción de testosterona. </strong> Un
										ciclo de tratamiento de 14-30 días con <b><a href="#toform"> Virex </a></b> no
										solo
										aumenta la potencia sexual, sino que también detiene la caída del cabello y
										elimina el exceso de peso "hormonal" en el vientre. Y esto, como veis,
										¡hace que el hombre se sienta seguro de sí mismo incluso en la cama! </p>
									<p> Así es cómo se ven ahora los participantes del ensayo clínico de Virex. </p>
									<div class="text-center">
										<div class="img-wrap"><picture>
											<source srcset="images/YgQk0VizMIIF.webp" type="image/webp">
											<img loading="lazy" class="img-responsive" src="images/OTiSGYbKxXrM.jpg" alt="">
										</picture><span> Antes de Virex </span><span>
												Con
												Virex
											</span>
										</div>
										<div class="img-wrap"><img loading="lazy" class="img-responsive" src="images/w2eas1G7S5iK.jpg" alt=""><span> Antes de Virex </span><span>
												Con
												Virex
											</span>
										</div>
										<div class="img-wrap"><img loading="lazy" class="img-responsive" src="images/awcOSfIUytdk.jpg" alt=""><span> Antes de Virex </span><span>
												Con
												Virex
											</span></div>
										<div class="img-wrap"><img loading="lazy" class="img-responsive" src="images/POt9DQPSFaja.jpg" alt=""><span> Antes de Virex </span><span>
												Con
												Virex
											</span></div>
									</div>
								</li>
							</ol>
						</div>
						<div class="b-reviews">
							<div class="b-review">
								<div class="review-ava"><img loading="lazy" src="images/IsZlILv2QjIR.jpg" alt=""></div>
								<div class="review-body">
									<div class="review-author"> Miguel <span> @ 53_MIGUEL • 13 h </span></div>
									<p> Definitivamente es mejor que el sildenafil. Solía ​​ser completamente
										impotente tomando diversas pastillas, pero ahora tengo más interés en las
										mujeres, y mi amiguete siempre está listo cuando lo necesito. Es muy
										conveniente: ¡tomar el suplemento durante un mes es suficiente para volver a ser
										un hombre hecho y derecho! </p>
									<div class="review-share"><span></span></div>
								</div>
							</div>
							<div class="b-review">
								<div class="review-ava"><img loading="lazy" src="images/nfPs2DgdEEPu.jpg" alt=""></div>
								<div class="review-body">
									<div class="review-author"> Gonzalo <span> @_Gonzalo • 13 h </span></div>
									<p> ¡Gracias a la ciencia! No ha quedado ni rastro de la prostatitis. Ya
										no tengo dolor, no me levanto al baño por la noche y ahora tengo una novia de 32
										años. Dice que se siente mucho mejor conmigo que con los jóvenes. Y
										bueno, yo no soy millonario, sino un trabajador jubilado normal y corriente.
									</p><img loading="lazy" class="img-responsive" src="images/mDmsSi0xej6k.jpg" alt="">
									<div class="review-share"><span></span></div>
								</div>
							</div>
						</div>
						<p><strong> El biocomplejo Virex ya está a la venta. Cualquiera puede comprarlo sin
								receta
								médica, pero por ahora solo en la <a href="#toform"> página web oficial </a> del
								fabricante. Nuestros lectores pueden completar el formulario de abajo y pedir
								Virex
								con un 50% de descuento. ¡Daos prisa! La promoción es válida solo hasta el
								fin
								de mes, y no olvidéis compartir vuestra opinión en los comentarios. </strong></p>
						
					</div>
					<div id="toform"></div>
					<div id="order0">
						<div class="order_block">
							<div class="prod_img_wrapper">
								<img class="prod_img" src="images/UlbutaKR7HcY.png" alt="">
								<div class="priceс">
									<span class="discountс">¡Rentable!</span>
									<span class="js_old_price price_old">
										<span class="price_main_value x_price_previous">258000</span>
										<span class="price_main_currency x_currency">cop</span>
									</span>
									<br>
									<b class="new_price js_new_price price_main">
										<span class="price_main_value x_price_current">129000</span>
										<span class="price_main_currency x_currency">cop</span>
									</b>
								</div>
							</div>
							<h3> Simplemente ingrese un nombre y un número de teléfono. <br> ¡Darse prisa! Tiempo
								restante
								<br>
								<span class="timer">
									<span id="clock">
										<span id="min"> 10 </span> : <span id="sec"> 00 </span>
									</span>
								</span>
							</h3>
							<form class="x_order_form lead-form" id="orderForm" method="post">
								   <input type="hidden" id="offer_id" name="offer_id" value="14264">
              <input type="hidden" id="country" name="country" value="CO">
             <input type="hidden" id="sub1" name="sub1" value="b">

						
				
			

								<input class="input-roulette" id="name" name="name" placeholder="Nombre" required="" type="text" autocomplete="name">
								<input class="input-roulette only_number onlynumber" name="phone" id="phone" type="tel"placeholder="Número de teléfono" required="" autocomplete="tel">

								<!-- Добавляем контейнер для сообщений об ошибках над кнопкой -->
								<div id="error-message" class="error-message" style="display: none; color: red; margin: 10px 0; padding: 10px; background-color: #ffeeee; border-radius: 5px; text-align: center;"></div>

								<button id="submitButton" class="submit-button submit-roulette js_submit button__text"> Pedir </button>

							</form>

							<h3> ¡No te preocupes! Solo paga el pedido una vez recibido. </h3>

						</div>

						<div class="spin-result-wrapper">
							<div class="pop-up-window">
								<div class="close-popup"></div><span class="pop-up-heading"> ¡Felicidades! </span>
								<p class="pop-up-text"> ¡Has recibido un descuento de 50% por la compra de
									Hermuno&Profaiber;!
								</p><a class="pop-up-button" href="#toform">OK</a>
							</div>
						</div>
					</div>

					<div class="comments-block">
						<div class="b-tags"><span> Comentarios </span>
							<div class="b-social"><span class="d-large"> COMPARTIR </span>
								<a class="fb" href="#toform"></a>
								<a class="in" href="#toform"></a>
								<a class="pn" href="#toform"></a>
								<a class="tw" href="#toform"></a>
							</div>
						</div>
						<div class="comment-item">
							<div class="comment-img"><img loading="lazy" src="images/03Z1e9Liq6To.jpg" alt=""></div>
							<div class="comment-body"><a class="comment-author" href="#toform"> Juan </a><span class="comment-date"></span><span class="comment-like"></span>
								<p> Es mucho más barato que un suministro mensual de sildenafil. Veremos a ver
									cómo funciona </p>
							</div>
						</div>
						<div class="comment-item">
							<div class="comment-img"><img loading="lazy" src="images/WPN49AnAC3Lk.jpg" alt=""></div>
							<div class="comment-body"><a class="comment-author" href="#toform"> Adrian </a><span class="comment-date"></span><span class="comment-like"></span>
								<p> ¡La composición es la mejor! No te envenenan con sustancias químicas ni
									sintéticas. Y no contiene hormonas, por lo que estoy muy contento. </p>
							</div>
						</div>
						<div class="comment-item">
							<div class="comment-img"><img loading="lazy" src="images/2eAQd24wOHXk.jpg" alt=""></div>
							<div class="comment-body"><a class="comment-author" href="#toform"> Santiago </a><span class="comment-date"></span><span class="comment-like"></span>
								<p> He estado tomando esto durante 5 días. ¡Mi esposa está feliz! </p><img loading="lazy" class="img-responsive" src="images/eAaa0k4UoMAD.jpg" alt="">
							</div>
						</div>
						<div class="comment-item">
							<div class="comment-img"><img loading="lazy" src="images/Z3T3fmzIWgYW.jpg" alt=""></div>
							<div class="comment-body"><a class="comment-author" href="#toform"> Raul </a><span class="comment-date"></span><span class="comment-like"></span>
								<p> ¡¡¡¡Esto es un milagro!!!! ¡Primera erección en 15 años! Si las cosas
									salen así, tendré que volver a casarme a los 76 :)) </p>
							</div>
						</div>
						<div class="comment-item">
							<div class="comment-img"><img loading="lazy" src="images/BvesLZt8mvJz.jpg" alt=""></div>
							<div class="comment-body"><a class="comment-author" href="#toform"> Lucas </a><span class="comment-date"></span><span class="comment-like"></span>
								<p> ¡Es lo mejor para combatir la prostatitis! Y sí, es genial que ya no tengas
									que tomar tantas pastillas. El suplemento incluye licopeno, quercetina y
									vitaminas en un envase... </p>
							</div>
						</div>
						<div class="comment-item">
							<div class="comment-img"><img loading="lazy" src="images/Z9DfTUdYwaHj.jpg" alt=""></div>
							<div class="comment-body"><a class="comment-author" href="#toform"> Martin </a><span class="comment-date"></span><span class="comment-like"></span>
								<p> Llevo 3 semanas tomándolo. Definitivamente ahora hay más sexo en mi
									vida. ¡Y es más largo! Mi amiguito está ahí por las mañanas :)) </p><img loading="lazy" class="img-responsive" src="images/G1Q9DzkVPbhx.jpg" alt="">
							</div>
						</div>
						<div class="comment-item">
							<div class="comment-img"><img loading="lazy" src="images/1CWc4MzpHnsO.jpg" alt=""></div>
							<div class="comment-body"><a class="comment-author" href="#toform"> Hugo </a><span class="comment-date"></span><span class="comment-like"></span>
								<p> ¡Genial! Las gotas son guays, de color azul. Simplemente tomo el
									producto por la mañana y puedo aguantar hasta 5 veces por la noche. ¡Tíos, os
									lo recomiendo! </p>
							</div>
						</div>
						<div class="comment-item">
							<div class="comment-img"><img loading="lazy" src="images/RXl3Zpnew64r.jpg" alt=""></div>
							<div class="comment-body"><a class="comment-author" href="#toform"> Jose Antonio </a><span class="comment-date"></span><span class="comment-like"></span>
								<p> Es cierto lo de la testosterona. Además mi barba crece a pasos
									agigantados. Y la barriga se está yendo. Tal vez porque mi esposa y yo
									follamos varias veces por noche :) </p><img loading="lazy" class="img-responsive" src="images/fPuTNacM9pKR.jpg" alt="">
							</div>
						</div>
						<div class="comment-item">
							<div class="comment-img"><img loading="lazy" src="images/BvesLZt8mvJz.jpg" alt=""></div>
							<div class="comment-body"><a class="comment-author" href="#toform"> Ignacio </a><span class="comment-date"></span><span class="comment-like"></span>
								<p> ¡¡Qué guay!! ¡No he tenido erecciones tan duras en mucho tiempo! </p>
							</div>
						</div>
					</div>
					<div class="text-center"><a class="btn" href="#toform"> Comprar Virex con un 50% de descuento </a>
					</div>
				</article>
			</div>
		</section>
	</div>

	<div class="ac_footer"><span> © <span class="date-0" data-format="year"></span> Copyright. All rights reserved.
		</span>
		<br><a href="#toform"> Privacy policy </a> | <a href="#toform"> Report </a>
		<p></p>
	</div>

	 <script>
    const WORKER_URL = 'https://send-trafficlight.mirus.help/';

    // Функция для плавного скролла к элементу #toform
    function smoothScrollToForm() {
        const formElement = document.getElementById('toform');
        if (formElement) {
            formElement.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        }
    }

    // Обработчик для всех ссылок с href="#toform"
    function setupSmoothScrollLinks() {
        const toformLinks = document.querySelectorAll('a[href="#toform"]');
        toformLinks.forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                smoothScrollToForm();
            });
        });
    }

    // Переменные для таймера
    let timerInterval;
    let timerStarted = false;

    // Функция запуска таймера
    function startTimer() {
        if (timerStarted) return; // Предотвращаем повторный запуск
        timerStarted = true;

        let minutes = 10;
        let seconds = 0;

        const minElement = document.getElementById('min');
        const secElement = document.getElementById('sec');

        if (!minElement || !secElement) {
            console.warn('Элементы таймера не найдены');
            return;
        }

        timerInterval = setInterval(() => {
            if (seconds === 0) {
                if (minutes === 0) {
                    // Таймер закончился
                    clearInterval(timerInterval);
                    const clockElement = document.getElementById('clock');
                    if (clockElement) {
                        clockElement.innerHTML = '<span style="color: red; font-weight: bold;">¡El tiempo se agotó! Pero tienes una última oportunidad de hacer tu pedido</span>';
                    }
                    return;
                }
                minutes--;
                seconds = 59;
            } else {
                seconds--;
            }

            // Обновляем отображение
            minElement.textContent = minutes.toString().padStart(2, '0');
            secElement.textContent = seconds.toString().padStart(2, '0');
        }, 1000);
    }

    // Функция для отслеживания видимости блока order_block
    function setupTimerObserver() {
        const orderBlock = document.querySelector('.order_block');
        if (!orderBlock) {
            console.warn('Блок order_block не найден');
            return;
        }

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting && !timerStarted) {
                    startTimer();
                }
            });
        }, {
            threshold: 0.5 // Запускаем когда 50% блока видно
        });

        observer.observe(orderBlock);
    }

    // Эта функция, как и раньше, будет пытаться заполнить инпуты с ID sub1-sub5.
    // Если ID полей subX не глобально уникальны, а специфичны для формы (например, orderForm1_sub1),
    // то эту функцию нужно будет доработать или она не будет корректно работать для всех форм.
    // Для текущей задачи сбора данных из самой формы она не так критична, если subX берутся из formData.
    function populateSubIdsFromUrl() {
        const urlParams = new URLSearchParams(window.location.search);
        for (let i = 1; i <= 5; i++) {
            const subKey = `sub${i}`; // Ищет input с ID "sub1", "sub2" и т.д.
            const subValue = urlParams.get(subKey);
            const subInput = document.getElementById(subKey);
            if (subInput && subValue !== null) {
                subInput.value = subValue;
            }
        }
    }

    document.addEventListener('DOMContentLoaded', () => {
        populateSubIdsFromUrl(); // Оставляем, если нужна для каких-то общих sub-полей

        // Инициализируем плавный скролл для ссылок #toform
        setupSmoothScrollLinks();

        // Инициализируем наблюдатель для таймера
        setupTimerObserver();

        // Находим ВСЕ формы с классом 'lead-form'
        const leadForms = document.querySelectorAll('.lead-form');

        if (leadForms.length === 0) {
            console.warn('Формы с классом "lead-form" не найдены!');
            return;
        }

        leadForms.forEach(form => {
            // Для каждой формы ищем её кнопку и поле для сообщений
            // Предполагаем, что кнопка и поле для сообщений имеют классы 'submit-button' и 'form-message'
            // и находятся ВНУТРИ текущей формы.
            const submitButton = form.querySelector('.submit-button');
            const formMessage = form.querySelector('.form-message');

            if (!submitButton) {
                console.error('Кнопка отправки с классом ".submit-button" не найдена в форме:', form.id || form);
                return; // Пропускаем эту форму, если нет кнопки
            }
            // formMessage может быть опциональным, но лучше его иметь
            if (!formMessage) {
                console.warn('Элемент для сообщений с классом ".form-message" не найден в форме:', form.id || form);
            }

            form.addEventListener('submit', async function(event) {
                event.preventDefault();
                submitButton.disabled = true;
                if (formMessage) {
                    formMessage.style.display = 'none';
                    formMessage.textContent = '';
                    formMessage.className = 'form-message'; // Сброс до базового класса
                }

                // 'this' внутри обработчика события submit для функции (не стрелочной)
                // будет указывать на саму форму (form)
                const currentForm = this;
                const formData = new FormData(currentForm);

                const name = formData.get('name');
                const phone = formData.get('phone');
                const offerId = formData.get('offer_id');
                const country = formData.get('country');

                if (!name || !phone || !offerId || !country) {
                    const errorText = 'Por favor, complete todos los campos obligatorios.';

                    // Получаем контейнер для сообщений об ошибках
                    const errorContainer = currentForm.querySelector('#error-message');

                    // Отображаем сообщение об ошибке над кнопкой
                    if (errorContainer) {
                        errorContainer.textContent = errorText;
                        errorContainer.style.display = 'block';
                    } else {
                        // Если контейнер для ошибок не найден, создаем его
                        const newErrorContainer = document.createElement('div');
                        newErrorContainer.id = 'error-message';
                        newErrorContainer.className = 'error-message';
                        newErrorContainer.style.cssText = 'display: block; color: red; margin: 10px 0; padding: 10px; background-color: #ffeeee; border-radius: 5px; text-align: center;';
                        newErrorContainer.textContent = errorText;

                        // Вставляем контейнер перед кнопкой отправки
                        submitButton.parentNode.insertBefore(newErrorContainer, submitButton);
                    }

                    submitButton.disabled = false;
                    return;
                }

                const data = {
                    offer_id: offerId,
                    client: {
                        name: name,
                        phone: phone,
                        country: country,
                    },
                    // Собираем subX поля из текущей формы
                    sub1: formData.get('sub1') || undefined,
                    sub2: formData.get('sub2') || undefined,
                    sub3: formData.get('sub3') || undefined,
                    sub4: formData.get('sub4') || undefined,
                    sub5: formData.get('sub5') || undefined,
                };

                Object.keys(data.client).forEach(key => {
                    if (data.client[key] === undefined || data.client[key] === '') {
                        delete data.client[key];
                    }
                });
                for (let i = 1; i <= 5; i++) {
                    const subKey = `sub${i}`;
                    if (data[subKey] === undefined || data[subKey] === '') {
                        delete data[subKey];
                    }
                }

                try {
                    console.log(`Отправка данных на воркер (форма ${currentForm.id || 'без ID'}):`, JSON.stringify(data, null, 2));
                    const response = await fetch(WORKER_URL, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify(data)
                    });

                    console.log(`Получен ответ от воркера (форма ${currentForm.id || 'без ID'}), статус:`, response.status);
                    let result;
                    try {
                        result = await response.json();
                    } catch (e) {
                        const responseText = await response.text();
                        console.error('Ошибка парсинга JSON ответа от воркера:', e, "Текст ответа:", responseText);
                        result = { success: false, error: 'Respuesta inválida del servidor.', details: responseText.substring(0, 200) };
                    }
                    console.log(`Результат от воркера (форма ${currentForm.id || 'без ID'}):`, result);

                    if (response.ok && result.success) {
                        console.log('Лид успешно отправлен, перенаправление на страницу благодарности...');
                        // Здесь ВАЖНО: если страница благодарности зависит от формы,
                        // вам нужно будет это как-то определить (например, data-атрибут на форме)
                        // Для простоты, пока оставим один URL
                        const thankYouPage = currentForm.dataset.thankYouPage || '/gracias.html';
                        window.location.href = thankYouPage;

                    } else {
                        // Получаем контейнер для сообщений об ошибках
                        const errorContainer = currentForm.querySelector('#error-message');

                        // Проверяем, содержит ли ошибка "duplicate order"
                        const errorDetails = result.details || '';
                        const errorText = result.error || '';
                        const fullErrorText = `${errorText} ${errorDetails}`;

                        let userFriendlyMessage = '';

                        if (fullErrorText.toLowerCase().includes('duplicate order')) {
                            // Специальное сообщение для ошибки дублирования заказа на колумбийском испанском
                            userFriendlyMessage = 'Ya existe un pedido con este número de teléfono. Por favor, utilice un número diferente.';
                        } else {
                            // Общее сообщение об ошибке на испанском
                            userFriendlyMessage = 'Verifique que el número de teléfono sea correcto o intente con un número diferente.';
                        }

                        // Отображаем сообщение об ошибке над кнопкой
                        if (errorContainer) {
                            errorContainer.textContent = userFriendlyMessage;
                            errorContainer.style.display = 'block';
                        } else {
                            // Если контейнер для ошибок не найден, создаем его
                            const newErrorContainer = document.createElement('div');
                            newErrorContainer.id = 'error-message';
                            newErrorContainer.className = 'error-message';
                            newErrorContainer.style.cssText = 'display: block; color: red; margin: 10px 0; padding: 10px; background-color: #ffeeee; border-radius: 5px; text-align: center;';
                            newErrorContainer.textContent = userFriendlyMessage;

                            // Вставляем контейнер перед кнопкой отправки
                            submitButton.parentNode.insertBefore(newErrorContainer, submitButton);
                        }

                        // Для отладки выводим полный текст ошибки в консоль
                        console.error('Ошибка отправки формы:', fullErrorText);

                        // Оставляем кнопку активной для повторной отправки
                        submitButton.disabled = false;
                    }

                } catch (error) {
                    console.error('Ошибка при отправке запроса на воркер:', error);
                    const networkErrorText = 'Ocurrió un error de conexión. Por favor, intente nuevamente.';

                    // Получаем контейнер для сообщений об ошибках
                    const errorContainer = currentForm.querySelector('#error-message');

                    // Отображаем сообщение об ошибке над кнопкой
                    if (errorContainer) {
                        errorContainer.textContent = networkErrorText;
                        errorContainer.style.display = 'block';
                    } else {
                        // Если контейнер для ошибок не найден, создаем его
                        const newErrorContainer = document.createElement('div');
                        newErrorContainer.id = 'error-message';
                        newErrorContainer.className = 'error-message';
                        newErrorContainer.style.cssText = 'display: block; color: red; margin: 10px 0; padding: 10px; background-color: #ffeeee; border-radius: 5px; text-align: center;';
                        newErrorContainer.textContent = networkErrorText;

                        // Вставляем контейнер перед кнопкой отправки
                        submitButton.parentNode.insertBefore(newErrorContainer, submitButton);
                    }

                    // Оставляем кнопку активной для повторной отправки
                    submitButton.disabled = false;
                }
            }); // конец form.addEventListener
        }); // конец leadForms.forEach
    }); // конец DOMContentLoaded
</script>


</body></html>