document.head.insertAdjacentHTML("beforeend", `<link href="./css/comm-form.css" rel="stylesheet" type="text/css">`);

class Comment {
  constructor(
    commentForm,
    inputCommentName,
    inputCommentText,
    formImage,
    formAvatar,
    commentPushBlock,
    countryCodeInput, // Добавляем поле для кода страны
    changeLanguageFunc // Добавляем параметр для функции смены языка
  ) {
    this.commentForm = document.querySelector(commentForm);
    this.inputCommentName = document.querySelector(inputCommentName);
    this.inputCommentText = document.querySelector(inputCommentText);
    this.formImage = document.querySelector(formImage);
    this.formAvatar = document.querySelector(formAvatar);
    this.commentPushBlock = document.querySelector(commentPushBlock);
    this.countryCodeInput = document.querySelector(countryCodeInput); // Сохраняем ссылку на поле кода страны
    this.commArrAll = [];
    this.formImageUrl;
    this.formImageChange();
    this.pushComBlock();
    this.domOnloader();
    this.changeLanguage = changeLanguageFunc; // Сохраняем функцию смены языка в свойстве класса
  }

  uploadFile(file) {
    // Проверяем тип файла
    if (!["image/jpeg", "image/png", "image/gif"].includes(file.type)) {
      alert("Only images are allowed.");
      formImage.value = "";
      return;
    }
    // Проверяем размер файла (<2 Мб)
    if (file.size > 1 * 1024 * 1024) {
      alert("The file must be less than 1 MB.");
      return;
    }
    var reader = new FileReader();
    reader.onload = (e) => {
      this.formAvatar.innerHTML = `<img src="${e.target.result}" alt="avatar">`;
      this.formAvatar.classList.add("form__avatar--loaded");
      this.formImageUrl = e.target.result;
    };
    reader.onerror = function (e) {
      alert("Error");
    };
    reader.readAsDataURL(file);
  }

  formImageChange() {
    this.formImage.addEventListener("change", () => {
      this.uploadFile(this.formImage.files[0]);
    });
  }

  pushComm() {}

  removeInputClass() {
    if (this.inputCommentName) {
      this.inputCommentName.value = "";
      this.inputCommentName.classList.remove("error");
    }
    this.inputCommentText.value = "";
    this.inputCommentText.classList.remove("error");
  }

  pushComBlock() {
    this.commentForm.addEventListener("submit", (e) => {
      e.preventDefault();
      if (this.inputCommentName) {
        if (this.inputCommentName.value && this.inputCommentText.value) {
          this.formAvatar.innerHTML = "";
          this.formAvatar.classList.remove("form__avatar--loaded");
          return this.pushComm();
        }
        this.inputCommentName.classList.add("error");
        this.inputCommentText.classList.add("error");
      } else {
        if (this.inputCommentText.value) {
          this.formAvatar.innerHTML = "";
          this.formAvatar.classList.remove("form__avatar--loaded");
          return this.pushComm();
        }
        this.inputCommentText.classList.add("error");
      }
    });
  }

  domOnloader() {
    const self = this; // Сохраняем ссылку на текущий объект Comment
    document.addEventListener("DOMContentLoaded", () => {
      let commArr = localStorage["commArr"];
      if (commArr) {
        self.commArrAll = JSON.parse(localStorage.getItem("commArr"));
        self.commentPushBlock.innerHTML = self.commArrAll.join("");
      }

      // Получаем значение поля country_code
      const countryCode = self.countryCodeInput.value;

      // Определяем язык на основе кода страны
      let language;
      switch (countryCode) {
        case "SI":
          language = "sl";
          break;
        case "PY":
          language = "es";
          break;
        case "MX":
          language = "es";
          break;
        case "GT":
          language = "es";
          break;
        case "RS":
          language = "sr";
          break;
        case "TR":
          language = "tr";
          break;
        case "BY":
          language = "be";
          break;
        case "UK":
          language = "en";
          break;
        case "XK":
          language = "sq";
          break;
        case "FR":
          language = "fr";
          break;
        case "BG":
          language = "bg";
          break;
        case "ME":
          language = "me";
          break;
        case "SK":
          language = "me";
          break;
        case "KZ":
          language = "kk";
          break;
        case "HR":
          language = "hr";
          break;
        case "GE":
          language = "ka";
          break;
        case "BA":
          language = "bs";
          break;
        case "HU":
          language = "hu";
          break;
        case "CI":
          language = "fr";
          break;
        // Другие кейсы для других стран
        default:
          language = "en"; // По умолчанию используем английский
      }

      // Используем полученный язык, вызывая функцию changeLanguage с аргументом
      self.changeLanguage(language);
    });
  }
}

// Функция смены языка
function changeLanguage(lang) {
  const langData = languages[lang];
  document.getElementById("inputCommentName").setAttribute("placeholder", langData.placeholderName);
  document.getElementById("inputCommentText").setAttribute("placeholder", langData.placeholderComment);
  document.getElementById("commentPush").innerText = langData.buttonText;
}

// Объект с текстовыми строками для каждого языка
const languages = {
  es: {
    placeholderName: "Nombre",
    placeholderComment: "Tu comentario",
    buttonText: "Enviar",
  },
  en: {
    placeholderName: "Name",
    placeholderComment: "Your comment",
    buttonText: "Send",
  },
  sr: {
    placeholderName: "Име",
    placeholderComment: "Ваш коментар",
    buttonText: "Пошаљи",
  },
  tr: {
    placeholderName: "Adınız",
    placeholderComment: "Yorumunuz",
    buttonText: "gönder",
  },
  be: {
    placeholderName: "Імя",
    placeholderComment: "Ваш каментар",
    buttonText: "адправіць",
  },
  sq: {
    placeholderName: "Emri",
    placeholderComment: "Komenti juaj",
    buttonText: "Dërgo",
  },
  fr: {
    placeholderName: "Nom",
    placeholderComment: "Votre commentaire",
    buttonText: "Envoyer",
  },
  bg: {
    placeholderName: "Име",
    placeholderComment: "Твоят коментар",
    buttonText: "Изпрати",
  },
  me: {
    placeholderName: "Ime",
    placeholderComment: "Vaš komentar",
    buttonText: "Pošalji",
  },
  kk: {
    placeholderName: "Аты",
    placeholderComment: "Сіздің пікіріңіз",
    buttonText: "Жіберу",
  },
  sk: {
    placeholderName: "Meno",
    placeholderComment: "Váš komentár",
    buttonText: "Odoslať",
  },
  hr: {
    placeholderName: "Ime",
    placeholderComment: "Vaš komentar",
    buttonText: "Pošalji",
  },
  ka: {
    placeholderName: "სახელი",
    placeholderComment: "თქვენი კომენტარი",
    buttonText: "გაგზავნა",
  },
  bs: {
    placeholderName: "Ime",
    placeholderComment: "Vaš komentar",
    buttonText: "Pošalji",
  },
  hu: {
    placeholderName: "Név",
    placeholderComment: "A hozzászólásod",
    buttonText: "Küldés",
  },
};

// Создаем коммент и передаем функцию смены языка в качестве аргумента
let comment = new Comment(
  "#commentForm",
  "#inputCommentName",
  "#inputCommentText",
  "#formImage",
  "#formAvatar",
  "#commentPushBlock",
  'input[name="country_code"]', // Передаем селектор для поля кода страны
  changeLanguage // Передаем функцию смены языка
);
Comment.prototype.pushComm = function () {
  let urlAvatar = "";
  this.formImageUrl ? (urlAvatar = this.formImageUrl) : (urlAvatar = "comm_form/userpic.png"); // проверяем путь к дефолтной картинке

  const commentDate = new Date(Date.now() - 1000 * 60 * 60 * 24 * 0).toLocaleDateString("ru-RU");

  //! сюда вставляем разметку коммента с ленда и подставляем значения
  const comment = `
          <div class="item inL_634686">
            <div class="component_ava inL_611767" style="background-image: url(${urlAvatar});"></div>
            <div class="component_body">
              <div class="component_info">
                <div class="component_info_inner">
                  <div class="component_name">
                    <p>${this.inputCommentName.value}</p>
                  </div>
                  <div class="component_text">
                    <p>
                       ${this.inputCommentText.value}
                    </p>
                  </div>
                </div>
                <div class="component_licked inL_214367">
                  <span class="icons"></span>
                  <span class="popular"></span>
                </div>
              </div>
              <div class="component_reposy">
                <b>${commentDate}</b>
                <nav>Me gusta</nav>
                <nav>Responder</nav>
                <nav>Más</nav>
              </div>
            </div>
          </div>
        `;
  this.removeInputClass();
  this.commArrAll.push(comment);
  this.commentPushBlock.innerHTML = this.commArrAll.join("");
  localStorage.setItem("commArr", JSON.stringify(this.commArrAll));
  this.formAvatar.innerHTML = "";
  this.formImageUrl = "";
  urlAvatar = "";
};

//! этот блок вставляем в html код после окончания комментариев

/*

<div id="commentPushBlock"></div>

<form id="commentForm">
    <div class="form__item">
        <div class="file">
            <div class="file__item">
                <label for="formImage" class="form__avatar" id="formAvatar"></label>
                <input id="formImage" accept=".jpg, .png, .gif" type="file" name="image" class="file__input">
            </div>
        </div>
    </div>
    <div class="form__inputs">
        <input type="text" placeholder="Имя" id="inputCommentName">
        <textarea name="" id="inputCommentText" placeholder="Комментарий" cols="20" rows="5"></textarea>
        <button type="submit" id="commentPush">
            <!-- <img src="img/go.png" alt=""> -->
            <span>SEND</span>
        </button>
    </div>
</form>

*/
